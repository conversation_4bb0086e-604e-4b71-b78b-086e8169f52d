# logs
/logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# runtime data
pids
*.pid
*.seed
*.pid.lock

# bower dependency directory (https://bower.io/)
bower_components

# compiled binary addons (https://nodejs.org/api/addons.html)
build
target
out
dist
*.tgz

# node/typescript/js
node_modules/
jspm_packages/
.node_repl_history
typings/
.grunt
.npm
.eslintcache
.next
.nuxt
.yarn-integrity
package-lock.json
locales
bin
build-env.yaml

# python
__pycache__

# kotlin
.kotlintest

# parcel-bundler cache (https://parceljs.org/)
.cache

# serverless directories
.serverless

# IDE / Editor
.idea
*.iml
*.iws
*.ipr

# gradle
.gradle
gradle*
m2

# service worker
sw.*

# macOS
.DS_Store

# vim swap files
*.swp

# jest junit
**/*/junit.xml

# ignore .env from GraphQL
**/*/GraphQL/.env
!NodeJs/Run/GraphQL/package-lock.json

# ignore .env
.env