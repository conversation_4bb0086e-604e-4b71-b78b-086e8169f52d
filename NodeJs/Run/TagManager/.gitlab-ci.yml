.NodeJs/Run/TagManager/deploy-tagging:
  extends:
    - .Kotlin/base-variables
  needs: []
  environment: $SERVICE_NAME/$ENVIRONMENT
  variables:
    SERVICE_NAME: "TagManager"
    # TODO: this path may get invalid as gcr.io is deprecated
    IMAGE: "gcr.io/cloud-tagging-********/gtm-cloud-image:stable"
  script:
    - export PREVIEW_SERVER_URL="https://${ENVIRONMENT}-tag-manager-preview-y7fnkx4lwq-ew.a.run.app";
    - echo $PREVIEW_SERVER_URL
    - gcloud run deploy "${ENVIRONMENT}-tag-manager"
      --region $CLOUD_REGION
      --image $IMAGE
      --platform managed
      --ingress all
      --min-instances 0
      --max-instances $MAX_INSTANCES
      --timeout 60
      --allow-unauthenticated
      --service-account <EMAIL>
      --set-env-vars PREVIEW_SERVER_URL="$PREVIEW_SERVER_URL",CONTAINER_CONFIG="$CONTAINER_CONFIG"

.NodeJs/Run/TagManager/deploy-preview:
  extends:
    - .Kotlin/base-variables
  needs: []
  environment: $SERVICE_NAME/$ENVIRONMENT
  variables:
    SERVICE_NAME: "TagManagerPreview"
    # TODO: this path may get invalid as gcr.io is deprecated
    IMAGE: "gcr.io/cloud-tagging-********/gtm-cloud-image:stable"
  script:
    - gcloud run deploy "${ENVIRONMENT}-tag-manager-preview"
      --region $CLOUD_REGION
      --image $IMAGE
      --platform managed
      --ingress all
      --min-instances 0
      --max-instances $MAX_INSTANCES
      --timeout 60
      --allow-unauthenticated
      --service-account <EMAIL>
      --set-env-vars CONTAINER_CONFIG="$CONTAINER_CONFIG",RUN_AS_PREVIEW_SERVER=true

NodeJs/Run/TagManager/deploy-preview-devel:
  stage: deploy-devel
  extends:
    - .NodeJs/Run/variables-devel
    - .run-always-on-main-or-manually
    - .NodeJs/Run/TagManager/deploy-preview
  needs: []
  variables:
    ENVIRONMENT: "devel"
    CONTAINER_CONFIG: "aWQ9R1RNLU5KTVM0QlZaJmVudj0xJmF1dGg9LWF1MG5JdnZLeDB4X1prcG5UQ095Zw=="
    MAX_INSTNACES: 1

NodeJs/Run/TagManager/deploy-preview-prod:
  stage: deploy-prod
  extends:
    - .NodeJs/Run/variables-prod
    - .run-only-manually
    - .NodeJs/Run/TagManager/deploy-preview
  needs: []
  variables:
    ENVIRONMENT: "prod"
    CONTAINER_CONFIG: "aWQ9R1RNLVdLUkwyRFQyJmVudj0xJmF1dGg9YWh2RS1TQ1pxU3k3OHNCWVVGR3Mwdw=="
    MAX_INSTNACES: 1

NodeJs/Run/TagManager/deploy-tag-devel:
  stage: deploy-devel
  extends:
    - .NodeJs/Run/variables-devel
    - .run-always-on-main-or-manually
    - .NodeJs/Run/TagManager/deploy-tagging
  needs: []
  variables:
    ENVIRONMENT: "devel"
    CONTAINER_CONFIG: "aWQ9R1RNLU5KTVM0QlZaJmVudj0xJmF1dGg9LWF1MG5JdnZLeDB4X1prcG5UQ095Zw=="
    MAX_INSTNACES: 1

NodeJs/Run/TagManager/deploy-tag-production:
  stage: deploy-prod
  extends:
    - .NodeJs/Run/variables-prod
    - .run-only-manually
    - .NodeJs/Run/TagManager/deploy-tagging
  needs: []
  variables:
    ENVIRONMENT: "prod"
    CONTAINER_CONFIG: "aWQ9R1RNLVdLUkwyRFQyJmVudj0xJmF1dGg9YWh2RS1TQ1pxU3k3OHNCWVVGR3Mwdw=="
    MAX_INSTANCES: 1
