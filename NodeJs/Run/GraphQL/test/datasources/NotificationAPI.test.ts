import nock from 'nock'
import { Environment } from '../../src/common/environment'
import { NotificationAPI } from '../../src/datasources/NotificationAPI'
import {
    NotificationResponse,
    NotificationType as NotificationDtoType,
    PagedNotificationResponse,
    StorageEntityType,
} from '../../src/generated/api'
import { PaginationModel } from '../../src/models/pagination'
import { NotificationModel, NotificationType } from '../../src/models/notification'
import { userResponse } from './test-utils'
import { Currency, NotificationTypeCategory, UserProfileType } from '../../src/generated/resolvers-types'

beforeEach(() => {
    nock.disableNetConnect()
})

describe('data source: NotificationAPI', () => {
    describe('method: getNotifications', () => {
        test("should make a call to fetch user's notifications", async () => {
            // given
            const userId = 'user-id'
            const notificationId = 'notification-id'
            const underTest = new NotificationAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v1/notifications`)
                .query({
                    pageSize: 4,
                    afterCursor: 'after-cursor',
                    beforeCursor: 'before-cursor',
                    categories: ['COMMENT', 'POST'],
                })
                .reply(200, pagedNotificationResponse(notificationId))

            // when
            const { notifications, pagination } = await underTest.getNotifications(
                {
                    first: 4,
                    before: 'before-cursor',
                    after: 'after-cursor',
                },
                {
                    categories: [NotificationTypeCategory.COMMENT, NotificationTypeCategory.POST],
                }
            )

            // then
            expect(notifications).toEqual<NotificationModel[]>([
                {
                    id: notificationId,
                    type: NotificationType.CANCELLED_SUBSCRIPTION_BY_CREATOR,
                    checkedAt: '2023-09-17T00:00:00Z',
                    createdAt: '2023-07-17T00:00:00Z',
                    seenAt: '2023-07-17T00:00:00Z',
                    actorCount: 15,
                    objectId: 'target-user-id',
                    objectType: StorageEntityType.USER,
                    lastActorId: 'last-actor-id',
                    lastActor: {
                        id: userId,
                        name: 'user-name',
                        bio: 'user-bio',
                        image: {
                            url: 'image-id',
                            width: 420,
                            height: 69,
                            hidden: true,
                        },
                        path: 'user-path',
                        subscribable: true,
                        verified: false,
                        counts: {
                            supporting: 150,
                            supporters: 21231,
                            supportersThreshold: 25000,
                            posts: 243,
                            ownedCommunities: 1,
                        },
                        hasRssFeed: true,
                        spotifyShowUri: 'spotify-uri',
                        tier: {
                            id: 'EUR05',
                            priceCents: 500,
                            hidden: false,
                            default: false,
                            currency: Currency.EUR,
                        },
                        categories: [
                            {
                                id: 'category-id',
                                name: 'category-name',
                                slug: 'slug',
                            },
                        ],
                        isDeleted: false,
                        privacyPolicyEnabled: false,
                        analytics: {
                            facebookPixelId: 'facebook-pixel-id',
                        },
                        hasGiftsAllowed: true,
                        emailPublic: '<EMAIL>',
                        profileType: UserProfileType.PUBLIC,
                    },
                },
            ])
            expect(pagination).toEqual<PaginationModel>({
                hasNextPage: false,
                startCursor: 'eyJmaXJzdElkIjoiaHVuZ2hvYW5nemZndm1kZW0tMTcwNjY4MjIxMCX',
                endCursor: 'eyJmaXJzdElkIjoiaHVuZ2hvYW5nemZndm1kZW0tMTcwNjY4MjIxMCJ9',
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: updateNotification', () => {
        test('should make a call to update a notification', async () => {
            // given
            const userId = 'user-id'
            const notificationId = 'notification-id'
            const underTest = new NotificationAPI(Environment.DEVEL, userId)
            const checkedAt = new Date(2023, 12)
            const seenAt = new Date(2023, 11)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .put(`/v1/notifications/${notificationId}`, {
                    checkedAt: checkedAt.toISOString(),
                    seenAt: seenAt.toISOString(),
                })
                .reply(200, notificationResponse(notificationId))

            // when
            const notification = await underTest.updateNotification(notificationId, checkedAt, seenAt)

            // then
            expect(notification).toEqual<NotificationModel>({
                id: notificationId,
                type: NotificationType.CANCELLED_SUBSCRIPTION_BY_CREATOR,
                createdAt: '2023-07-17T00:00:00Z',
                checkedAt: '2023-09-17T00:00:00Z',
                seenAt: '2023-07-17T00:00:00Z',
                actorCount: 15,
                objectId: 'target-user-id',
                objectType: StorageEntityType.USER,
                lastActorId: 'last-actor-id',
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: markAllSeen', () => {
        test('should call api to mark all notifications as seen', async () => {
            // given
            const userId = 'user-id'
            const underTest = new NotificationAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .post(`/v1/users/${userId}/mark-notifications-seen`)
                .reply(200)

            // when
            await underTest.markAllSeen(userId)

            expect(scope.isDone()).toBeTruthy()
        })
    })
})

function notificationResponse(notificationId: string): NotificationResponse {
    return {
        id: notificationId,
        type: NotificationDtoType.CANCELLED_SUBSCRIPTION_BY_CREATOR,
        checkedAt: '2023-09-17T00:00:00Z',
        actorCount: 15,
        seenAt: '2023-07-17T00:00:00Z',
        createdAt: '2023-07-17T00:00:00Z',
        lastActor: userResponse('user-id'),
        relationships: {
            objectId: 'target-user-id',
            objectType: StorageEntityType.USER,
            lastActorId: 'last-actor-id',
        },
    }
}

function pagedNotificationResponse(notificationId: string): PagedNotificationResponse {
    return {
        hasNext: false,
        afterCursor: 'eyJmaXJzdElkIjoiaHVuZ2hvYW5nemZndm1kZW0tMTcwNjY4MjIxMCJ9',
        beforeCursor: 'eyJmaXJzdElkIjoiaHVuZ2hvYW5nemZndm1kZW0tMTcwNjY4MjIxMCX',
        content: [notificationResponse(notificationId)],
    }
}
