import nock from 'nock'
import { Environment } from '../../src/common/environment'
import { SessionAPI } from '../../src/datasources/SessionAPI'

beforeEach(() => {
    nock.disableNetConnect()
})

describe('data source: SessionAPI', () => {
    describe('method: deleteSession', () => {
        test('should make a call to delete a session', async () => {
            // given
            const userId = 'user-id'
            const sessionId = 'session-id-123'
            const underTest = new SessionAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-auth-90568653510.europe-west1.run.app')
                .delete(`/v1/sessions/${sessionId}`)
                .reply(204)

            // when
            await underTest.deleteSession(sessionId)

            // then
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: deleteSessions', () => {
        test('should make a call to delete all sessions except current one', async () => {
            // given
            const userId = 'user-id'
            const underTest = new SessionAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-auth-90568653510.europe-west1.run.app').delete(`/v1/sessions`).reply(204)

            // when
            await underTest.deleteSessions()

            // then
            expect(scope.isDone()).toBeTruthy()
        })
    })
})
