// Jest <PERSON>nap<PERSON> v1, https://goo.gl/fbAQLP

exports[`query: adminUserDetails 1`] = `
{
  "adminUserDetails": {
    "bio": "user-bio",
    "counts": {
      "incomes": 21,
      "incomesClean": 421,
      "invoices": 121,
      "payments": 300,
      "posts": 243,
      "supporters": 21231,
      "supporting": 150,
    },
    "discord": {
      "id": "discord-id",
    },
    "hasRssFeed": true,
    "id": "user-id",
    "image": {
      "url": "image-id",
    },
    "livestream": {
      "streamKey": "stream-key",
      "streamUrl": "stream-url",
    },
    "name": "user-name",
    "path": "user-path",
    "role": "MODERATOR",
  },
}
`;

exports[`query: comment 1`] = `
{
  "comment": {
    "__typename": "Comment",
    "counts": {
      "comments": 10,
      "replies": 10,
    },
    "id": "comment-id",
    "publishedAt": 2021-02-26T19:21:45.441Z,
    "state": "DELETED",
    "user": {
      "id": "user-id",
      "name": "",
    },
  },
}
`;

exports[`query: comments 1`] = `
{
  "comments": {
    "nodes": [
      {
        "__typename": "Comment",
        "counts": {
          "comments": 10,
          "replies": 10,
        },
        "id": "comment-id",
        "publishedAt": 2021-02-26T19:21:45.441Z,
        "state": "DELETED",
        "textMarkdown": "text",
        "user": {
          "id": "user-id",
          "name": "user-name",
        },
      },
    ],
    "pageInfo": {
      "endCursor": "endCursor",
      "hasNextPage": false,
    },
  },
}
`;

exports[`query: community find by communityId 1`] = `
{
  "community": {
    "description": "A test community description",
    "id": "community-id",
    "image": {
      "height": 600,
      "hidden": false,
      "url": "community-image-id",
      "width": 800,
    },
    "isMember": true,
    "isVerified": true,
    "membersCount": 42,
    "name": "Test Community",
    "owner": {
      "id": "owner-id",
      "name": "user-name",
    },
    "slug": "test-community",
  },
}
`;

exports[`query: community find by slug 1`] = `
{
  "community": {
    "description": "A test community description",
    "id": "community-id",
    "image": {
      "height": 600,
      "hidden": false,
      "url": "community-image-id",
      "width": 800,
    },
    "isMember": true,
    "isVerified": true,
    "membersCount": 42,
    "name": "Test Community",
    "owner": {
      "id": "owner-id",
      "name": "user-name",
    },
    "slug": "test-community",
  },
}
`;

exports[`query: expectedIncome 1`] = `
{
  "expectedIncome": {
    "grossIncomeCents": 932,
    "netIncomeCents": 820,
  },
}
`;

exports[`query: featuredCreators locale is not passed, users details are fetched 1`] = `
{
  "featuredCreators": {
    "nodes": [
      {
        "categories": [
          {
            "name": "category-name",
          },
        ],
        "counts": {
          "posts": 40,
          "supporters": 50,
          "supporting": 10,
        },
        "id": "user-id",
        "name": "user-name",
        "tier": {
          "currency": "EUR",
          "id": "EUR05",
          "priceCents": 500,
        },
        "verified": true,
      },
    ],
  },
}
`;

exports[`query: featuredCreators locale is passed in, user api is not called 1`] = `
{
  "featuredCreators": {
    "nodes": [
      {
        "categories": [
          {
            "name": "category-name",
          },
        ],
        "counts": {
          "posts": 40,
          "supporters": 50,
          "supporting": 10,
        },
        "id": "user-id",
        "name": "user-name",
        "tier": {
          "currency": "EUR",
          "id": "EUR05",
          "priceCents": 500,
        },
        "verified": true,
      },
    ],
  },
}
`;

exports[`query: featuredCreatorsRandomized 1`] = `
{
  "featuredCreatorsRandomized": {
    "users": [
      {
        "name": "user-name",
      },
    ],
  },
}
`;

exports[`query: gjirafaVideoQualities 1`] = `
{
  "gjirafaVideoQualities": [
    {
      "duration": 1119.914667,
      "quality": "2160p",
      "size": 3512201772,
    },
  ],
}
`;

exports[`query: livestreams 1`] = `
{
  "livestreams": {
    "nodes": [
      {
        "__typename": "CompleteContentPost",
        "categories": [
          {
            "id": "category-id",
            "name": "category-name",
            "slug": "category-slug",
          },
        ],
        "counts": {
          "comments": 10,
          "replies": 10,
        },
        "id": "post-id",
        "pinnedAt": null,
        "publishedAt": 2021-02-26T19:21:45.441Z,
        "state": "DELETED",
        "text": null,
      },
    ],
    "pageInfo": {
      "endCursor": "endCursor",
      "hasNextPage": false,
    },
  },
}
`;

exports[`query: messageThread 1`] = `
{
  "messageThread": {
    "canMessage": true,
    "checkedAt": 2023-11-30T17:10:47.109Z,
    "createdAt": 2023-05-10T12:12:11.844Z,
    "id": "message-thread-id",
    "lastMessage": {
      "assets": [
        {
          "height": 100,
          "url": "image-url",
          "width": 150,
        },
        {
          "documentUrl": "document-url",
          "name": "document-name",
          "thumbnailUrl": null,
          "type": "DOCX",
        },
        {
          "audioByteSize": null,
          "audioStaticUrl": "gjirafa-audio-static-url",
          "audioStreamUrl": "gjirafa-audio-stream-url",
          "hasAudio": true,
          "hasVideo": true,
          "id": "id",
          "videoStreamUrl": "gjirafa-video-url",
        },
        {
          "price": null,
        },
      ],
      "id": "last-message-id",
      "text": "text",
    },
    "participants": [
      {
        "bio": "user-bio",
        "name": "user-name",
      },
      {
        "bio": "user-bio",
        "name": "user-name",
      },
    ],
    "seenAt": 2023-10-30T17:10:47.109Z,
  },
}
`;

exports[`query: messageThreads 1`] = `
{
  "messageThreads": {
    "nodes": [
      {
        "checkedAt": 2023-11-30T17:10:47.109Z,
        "createdAt": 2023-05-10T12:12:11.844Z,
        "id": "1683720731840-1319805956",
        "lastMessage": {
          "assets": [
            {
              "height": 100,
              "url": "image-url",
              "width": 150,
            },
            {
              "documentUrl": "document-url",
              "name": "document-name",
              "thumbnailUrl": null,
              "type": "DOCX",
            },
            {
              "audioByteSize": null,
              "audioStaticUrl": "gjirafa-audio-static-url",
              "audioStreamUrl": "gjirafa-audio-stream-url",
              "hasAudio": true,
              "hasVideo": true,
              "id": "id",
              "videoStreamUrl": "gjirafa-video-url",
            },
            {
              "price": null,
            },
          ],
          "id": "last-message-id",
          "text": "text",
        },
        "participants": [
          {
            "bio": "user-bio",
            "name": "user-name",
          },
          {
            "bio": "user-bio",
            "name": "user-name",
          },
        ],
        "seenAt": 2023-10-30T17:10:47.109Z,
      },
    ],
    "pageInfo": {
      "endCursor": "endCursor",
      "hasNextPage": false,
      "startCursor": null,
    },
  },
}
`;

exports[`query: messages 1`] = `
{
  "messages": {
    "nodes": [
      {
        "assets": [
          {
            "__typename": "PostImageAsset",
          },
          {
            "__typename": "PostDocumentAsset",
          },
          {
            "__typename": "PostGjirafaAsset",
          },
          {
            "__typename": "MessageLockedAsset",
          },
        ],
        "id": "message-id",
        "price": null,
        "sentAt": 2021-02-26T19:21:45.441Z,
        "sentBy": {
          "id": "user-id",
          "image": null,
          "name": "user-name",
          "subscribable": true,
        },
        "text": "text",
      },
    ],
    "pageInfo": {
      "endCursor": "endCursor",
      "hasNextPage": false,
    },
  },
}
`;

exports[`query: notificationSettings 1`] = `
{
  "notificationSettings": {
    "emailNewDm": true,
    "emailNewPost": false,
    "newsletter": true,
    "pushNewComment": false,
    "pushNewPost": true,
    "termsChanged": false,
  },
}
`;

exports[`query: notifications 1`] = `
{
  "notifications": {
    "nodes": [
      {
        "actorCount": 10,
        "checkedAt": 2023-10-06T06:33:10.926Z,
        "createdAt": 2021-02-27T19:21:45.441Z,
        "id": "notification-id-1",
        "seenAt": 2022-01-04T07:21:32.135Z,
      },
    ],
    "pageInfo": {
      "endCursor": null,
      "hasNextPage": false,
      "startCursor": null,
    },
  },
}
`;

exports[`query: post 1`] = `
{
  "post": {
    "__typename": "CompleteContentPost",
    "comments": {
      "nodes": [
        {
          "id": "comment-id",
          "text": null,
        },
      ],
    },
    "counts": {
      "comments": 10,
      "replies": 10,
    },
    "id": "post-id",
    "isAgeRestricted": false,
    "isSponsored": true,
    "markdown": "&#x200B;**text**&#x200B;",
    "pinnedAt": null,
    "publishedAt": 2021-02-26T19:21:45.441Z,
    "savedPostInfo": {
      "savedAt": 2023-09-18T00:00:00.000Z,
    },
    "state": "DELETED",
    "text": null,
    "textHtml": "<strong>text</strong>",
  },
}
`;

exports[`query: posts 1`] = `
{
  "posts": {
    "nodes": [
      {
        "__typename": "CompleteContentPost",
        "categories": [
          {
            "id": "category-id",
            "name": "category-name",
            "slug": "category-slug",
          },
        ],
        "counts": {
          "comments": 10,
          "replies": 10,
        },
        "id": "post-id",
        "pinnedAt": null,
        "publishedAt": 2021-02-26T19:21:45.441Z,
        "state": "DELETED",
        "text": null,
      },
    ],
    "pageInfo": {
      "endCursor": "endCursor",
      "hasNextPage": false,
    },
  },
}
`;

exports[`query: savedPosts 1`] = `
{
  "savedPosts": {
    "nodes": [
      {
        "post": {
          "id": "post-id",
          "pinnedAt": null,
          "publishedAt": 2021-02-26T19:21:45.441Z,
          "state": "DELETED",
        },
        "savedAt": 2023-05-10T12:12:11.844Z,
      },
    ],
  },
}
`;

exports[`query: searchUsers 1`] = `
{
  "searchUsers": {
    "nodes": [
      {
        "categories": [
          {
            "name": "category-name",
          },
        ],
        "counts": {
          "posts": 40,
          "supporters": 50,
          "supporting": 10,
        },
        "id": "user-id",
        "name": "user-name",
        "tier": {
          "currency": "EUR",
          "id": "EUR05",
          "priceCents": 500,
        },
        "verified": true,
      },
    ],
  },
}
`;

exports[`query: subscribeRequests 1`] = `
{
  "subscribeRequests": {
    "nodes": [
      {
        "createdAt": 2023-09-17T00:00:00.000Z,
        "id": "123",
      },
    ],
    "pageInfo": {
      "endCursor": null,
      "hasNextPage": false,
      "startCursor": null,
    },
  },
}
`;

exports[`query: subscribers full access on subscribers 1`] = `
{
  "subscribers": {
    "nodes": [
      {
        "couponAppliedForMonths": 5,
        "couponMethod": null,
        "couponPercentOff": null,
        "creator": {
          "id": "creator-id",
          "name": "user-name",
        },
        "id": "subscription-id",
        "status": "ACTIVE",
        "subscribedAt": 2023-05-11T12:12:11.844Z,
        "subscriber": {
          "id": "subscriber-id",
          "name": "user-name",
        },
        "tier": {
          "currency": "EUR",
          "id": "EUR05",
          "priceCents": 500,
        },
        "type": "STRIPE",
      },
    ],
    "pageInfo": {
      "endCursor": "endCursor",
      "hasNextPage": false,
      "startCursor": null,
    },
  },
}
`;

exports[`query: subscribers limited access on subscribers 1`] = `
{
  "subscribers": {
    "nodes": [
      {
        "creator": {
          "id": "creator-id",
          "name": "user-name",
        },
        "id": "subscription-id",
        "subscribedAt": 2023-05-11T12:12:11.844Z,
        "subscriber": {
          "id": "subscriber-id",
          "name": "user-name",
        },
      },
    ],
    "pageInfo": {
      "endCursor": "endCursor",
      "hasNextPage": false,
      "startCursor": null,
    },
  },
}
`;

exports[`query: subscriptions expired subscriptions 1`] = `
{
  "subscriptions": {
    "nodes": [
      {
        "id": "subscription-id",
      },
    ],
  },
}
`;

exports[`query: subscriptions full access on subscriptions 1`] = `
{
  "subscriptions": {
    "nodes": [
      {
        "couponAppliedForMonths": 5,
        "couponMethod": null,
        "couponPercentOff": null,
        "creator": {
          "id": "creator-id",
          "name": "user-name",
        },
        "id": "subscription-id",
        "status": "ACTIVE",
        "subscribedAt": 2023-05-11T12:12:11.844Z,
        "subscriber": {
          "id": "subscriber-id",
          "name": "user-name",
        },
        "tier": {
          "currency": "EUR",
          "id": "EUR05",
          "priceCents": 500,
        },
        "type": "STRIPE",
      },
    ],
    "pageInfo": {
      "endCursor": "endCursor",
      "hasNextPage": false,
      "startCursor": null,
    },
  },
}
`;

exports[`query: subscriptions limited access on subscriptions 1`] = `
{
  "subscriptions": {
    "nodes": [
      {
        "creator": {
          "id": "creator-id",
          "name": "user-name",
        },
        "id": "subscription-id",
        "subscribedAt": 2023-05-11T12:12:11.844Z,
        "subscriber": {
          "id": "subscriber-id",
          "name": "user-name",
        },
      },
    ],
    "pageInfo": {
      "endCursor": "endCursor",
      "hasNextPage": false,
      "startCursor": null,
    },
  },
}
`;

exports[`query: user 1`] = `
{
  "user": {
    "analytics": {
      "facebookPixelId": null,
    },
    "bio": "user-bio",
    "bioMarkdown": "user-bio",
    "categories": [
      {
        "name": "category-name",
      },
    ],
    "counts": {
      "posts": 40,
      "supporters": 50,
      "supporting": 10,
    },
    "emailPublic": "<EMAIL>",
    "hasGiftsAllowed": false,
    "id": "user-id",
    "name": "user-name",
    "privacyPolicyEnabled": false,
    "profileType": "PUBLIC",
    "subscription": {
      "creator": {
        "id": "creator-id",
        "name": "user-name",
      },
      "id": "subscription-id",
      "subscriber": {
        "id": "subscriber-id",
        "name": "user-name",
      },
    },
    "tier": {
      "currency": "EUR",
      "id": "EUR05",
      "priceCents": 500,
    },
    "verified": true,
  },
}
`;

exports[`query: viewer 1`] = `
{
  "viewer": {
    "bio": "user-bio",
    "bioMarkdown": "user-bio",
    "counts": {
      "incomes": 21,
      "incomesClean": 421,
      "invoices": 121,
      "payments": 300,
      "posts": 243,
      "supporters": 21231,
      "supporting": 150,
    },
    "discord": {
      "id": "discord-id",
    },
    "emailInvoice": "<EMAIL>",
    "emailPublic": "<EMAIL>",
    "hasRssFeed": true,
    "id": "requester",
    "image": {
      "url": "image-id",
    },
    "isOfAge": true,
    "livestream": {
      "playbackUrl": "playback-url",
      "streamKey": "stream-key",
      "streamUrl": "stream-url",
    },
    "name": "user-name",
    "path": "user-path",
    "role": "MODERATOR",
  },
}
`;
