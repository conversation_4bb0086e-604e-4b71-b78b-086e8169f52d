// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`mutation: assetTimestampUpdate 1`] = `
{
  "assetTimestampUpdate": null,
}
`;

exports[`mutation: categoryCreate 1`] = `
{
  "categoryCreate": {
    "category": {
      "id": "new",
      "name": "name",
      "slug": "slug",
    },
  },
}
`;

exports[`mutation: categoryDelete 1`] = `
{
  "categoryDelete": {
    "success": true,
  },
}
`;

exports[`mutation: categoryUpdate 1`] = `
{
  "categoryUpdate": {
    "category": {
      "id": "id",
      "name": "name",
      "slug": "slug",
    },
  },
}
`;

exports[`mutation: categoryUpdate 2`] = `
{
  "categoriesOrder": {
    "success": true,
  },
}
`;

exports[`mutation: commentCreate 1`] = `
{
  "commentCreate": {
    "comment": {
      "id": "comment-id",
    },
  },
}
`;

exports[`mutation: commentDelete 1`] = `
{
  "commentDelete": null,
}
`;

exports[`mutation: commentUpdate 1`] = `
{
  "commentUpdate": {
    "comment": {
      "id": "comment-id",
    },
  },
}
`;

exports[`mutation: communityCreate 1`] = `
{
  "communityCreate": {
    "community": {
      "description": "A test community description",
      "id": "new-community-id",
      "isVerified": true,
      "membersCount": 42,
      "name": "Test Community",
      "slug": "test-community",
    },
  },
}
`;

exports[`mutation: messageCreate 1`] = `
{
  "messageCreate": {
    "message": {
      "id": "test-message-id",
      "text": "text",
    },
  },
}
`;

exports[`mutation: messageThreadCreate 1`] = `
{
  "messageThreadUpsert": {
    "messageThread": {
      "id": "message-thread-id",
    },
  },
}
`;

exports[`mutation: messageThreadMarkAllSeen 1`] = `
{
  "messageThreadMarkAllSeen": {
    "success": true,
  },
}
`;

exports[`mutation: notificationMarkAllSeen 1`] = `
{
  "notificationMarkAllSeen": null,
}
`;

exports[`mutation: notificationSettingsUpdate 1`] = `
{
  "notificationSettingsUpdate": {
    "success": true,
  },
}
`;

exports[`mutation: notificationUpdate 1`] = `
{
  "notificationUpdate": null,
}
`;

exports[`mutation: pollCastVotes 1`] = `
{
  "pollCastVotes": {
    "success": true,
  },
}
`;

exports[`mutation: pollEnd 1`] = `
{
  "pollEnd": {
    "success": true,
  },
}
`;

exports[`mutation: postAddToLibrary 1`] = `
{
  "postAddToLibrary": {
    "savedPost": {
      "post": {
        "id": "vojtechknyttlnjirpwodtttilcziivdlbpeja",
      },
      "savedAt": 2023-10-06T06:33:10.926Z,
    },
  },
}
`;

exports[`mutation: postCastVote voteType: DOWN 1`] = `
{
  "postCastVote": {
    "success": true,
  },
}
`;

exports[`mutation: postCastVote voteType: NONE 1`] = `
{
  "postCastVote": {
    "success": true,
  },
}
`;

exports[`mutation: postCastVote voteType: UP 1`] = `
{
  "postCastVote": {
    "success": true,
  },
}
`;

exports[`mutation: postCreate 1`] = `
{
  "postCreate": {
    "post": {
      "id": "post-id",
    },
  },
}
`;

exports[`mutation: postDelete 1`] = `
{
  "postDelete": null,
}
`;

exports[`mutation: postRemoveFromLibrary 1`] = `
{
  "postRemoveFromLibrary": null,
}
`;

exports[`mutation: postUpdate full update 1`] = `
{
  "postUpdate": {
    "post": {
      "id": "post-id",
    },
  },
}
`;

exports[`mutation: postUpdate should only remove pin, other values should be used from the original 1`] = `
{
  "postUpdate": {
    "post": {
      "id": "post-id",
    },
  },
}
`;

exports[`mutation: rssFeedUrlGenerate 1`] = `
{
  "rssFeedUrlGenerate": {
    "rssFeedUrl": {
      "url": "feed-url",
    },
  },
}
`;

exports[`mutation: sessionRevoke 1`] = `
{
  "sessionRevoke": {
    "success": true,
  },
}
`;

exports[`mutation: subscribeRequestAccept 1`] = `
{
  "subscribeRequestAccept": {
    "success": true,
  },
}
`;

exports[`mutation: subscribeRequestAcceptAll 1`] = `
{
  "subscribeRequestAcceptAll": {
    "success": true,
  },
}
`;

exports[`mutation: subscribeRequestCancel 1`] = `
{
  "subscribeRequestCancel": {
    "success": true,
  },
}
`;

exports[`mutation: subscribeRequestCreate 1`] = `
{
  "subscribeRequestCreate": {
    "success": true,
  },
}
`;

exports[`mutation: subscribeRequestDecline 1`] = `
{
  "subscribeRequestDecline": {
    "success": true,
  },
}
`;

exports[`mutation: subscribeRequestMarkAllSeen 1`] = `
{
  "subscribeRequestMarkAllSeen": null,
}
`;

exports[`mutation: subscriberDelete 1`] = `
{
  "subscriberDelete": {
    "subscription": {
      "cancelAtPeriodEnd": true,
      "couponAppliedForMonths": 10,
      "creator": {
        "id": "creator-id",
      },
      "expires": 2023-10-10T06:33:10.926Z,
      "id": "subscription-id",
      "status": "INACTIVE",
      "subscribedAt": 2023-11-06T06:33:10.926Z,
      "subscriber": {
        "id": "subscriber-id",
      },
      "tier": {
        "currency": "EUR",
        "id": "EUR03",
        "priceCents": 500,
      },
      "type": "STRIPE",
    },
    "success": true,
  },
}
`;

exports[`mutation: subscriptionCancel forever free trial is immediately canceled 1`] = `
{
  "subscriptionCancel": {
    "subscription": {
      "cancelAtPeriodEnd": true,
      "couponAppliedForMonths": 5,
      "creator": {
        "id": "creator-id",
      },
      "expires": 2023-10-10T06:33:10.926Z,
      "id": "subscription-id",
      "status": "INACTIVE",
      "subscribedAt": 2023-11-06T06:33:10.926Z,
      "subscriber": {
        "id": "subscriber-id",
      },
      "tier": {
        "currency": "EUR",
        "id": "EUR05",
        "priceCents": 500,
      },
      "type": "STRIPE",
    },
    "success": true,
  },
}
`;

exports[`mutation: subscriptionCancel private profile subscription is immediately cancelled 1`] = `
{
  "subscriptionCancel": {
    "subscription": {
      "cancelAtPeriodEnd": true,
      "couponAppliedForMonths": 10,
      "creator": {
        "id": "creator-id",
      },
      "expires": 2023-10-10T06:33:10.926Z,
      "id": "subscription-id",
      "status": "INACTIVE",
      "subscribedAt": 2023-11-06T06:33:10.926Z,
      "subscriber": {
        "id": "subscriber-id",
      },
      "tier": {
        "currency": "EUR",
        "id": "EUR00",
        "priceCents": 500,
      },
      "type": "SUBSCRIBE_REQUEST",
    },
    "success": true,
  },
}
`;

exports[`mutation: subscriptionCancel subscription is active 1`] = `
{
  "subscriptionCancel": {
    "subscription": {
      "cancelAtPeriodEnd": true,
      "couponAppliedForMonths": 10,
      "creator": {
        "id": "creator-id",
      },
      "expires": 2023-10-10T06:33:10.926Z,
      "id": "subscription-id",
      "status": "INACTIVE",
      "subscribedAt": 2023-11-06T06:33:10.926Z,
      "subscriber": {
        "id": "subscriber-id",
      },
      "tier": {
        "currency": "EUR",
        "id": "EUR03",
        "priceCents": 500,
      },
      "type": "STRIPE",
    },
    "success": true,
  },
}
`;

exports[`mutation: subscriptionCancel subscription is past_due 1`] = `
{
  "subscriptionCancel": {
    "subscription": {
      "cancelAtPeriodEnd": true,
      "couponAppliedForMonths": 10,
      "creator": {
        "id": "creator-id",
      },
      "expires": 2023-10-10T06:33:10.926Z,
      "id": "subscription-id",
      "status": "INACTIVE",
      "subscribedAt": 2023-11-06T06:33:10.926Z,
      "subscriber": {
        "id": "subscriber-id",
      },
      "tier": {
        "currency": "EUR",
        "id": "EUR03",
        "priceCents": 500,
      },
      "type": "STRIPE",
    },
    "success": true,
  },
}
`;

exports[`mutation: subscriptionRenew  should call backend to renew the subscription 1`] = `
{
  "subscriptionRenew": {
    "subscription": {
      "cancelAtPeriodEnd": false,
      "couponAppliedForMonths": 10,
      "creator": {
        "id": "creator-id",
      },
      "expires": 2023-10-10T06:33:10.926Z,
      "id": "subscription-id",
      "status": "ACTIVE",
      "subscribedAt": 2023-11-06T06:33:10.926Z,
      "subscriber": {
        "id": "subscriber-id",
      },
      "tier": {
        "currency": "EUR",
        "id": "EUR03",
        "priceCents": 500,
      },
      "type": "STRIPE",
    },
    "success": true,
  },
}
`;

exports[`mutation: subscriptionRenew  subscription is active, no call to backend should be done to patch 1`] = `
{
  "subscriptionRenew": {
    "subscription": {
      "cancelAtPeriodEnd": false,
      "couponAppliedForMonths": 10,
      "creator": {
        "id": "creator-id",
      },
      "expires": 2023-05-10T12:12:11.844Z,
      "id": "subscription-id",
      "status": "ACTIVE",
      "subscribedAt": 2023-11-06T06:33:10.926Z,
      "subscriber": {
        "id": "subscriber-id",
      },
      "tier": {
        "currency": "EUR",
        "id": "EUR03",
        "priceCents": 500,
      },
      "type": "STRIPE",
    },
    "success": true,
  },
}
`;

exports[`mutation: viewerUpdate all inputs are assigned 1`] = `
{
  "viewerUpdate": {
    "errors": null,
    "userDetails": {
      "bio": "user-bio",
      "id": "user-id",
      "name": "user-name",
      "path": "user-path",
    },
  },
}
`;

exports[`mutation: viewerUpdate emailInvoice is set to null 1`] = `
{
  "viewerUpdate": {
    "userDetails": {
      "id": "user-id",
    },
  },
}
`;

exports[`mutation: viewerUpdate emailPublic is set to null 1`] = `
{
  "viewerUpdate": {
    "userDetails": {
      "id": "user-id",
    },
  },
}
`;

exports[`mutation: viewerUpdate only bio is passed 1`] = `
{
  "viewerUpdate": {
    "userDetails": {
      "id": "user-id",
    },
  },
}
`;

exports[`mutation: viewerUpdate only emailInvoice is passed 1`] = `
{
  "viewerUpdate": {
    "userDetails": {
      "id": "user-id",
    },
  },
}
`;

exports[`mutation: viewerUpdate only emailPublic is passed 1`] = `
{
  "viewerUpdate": {
    "userDetails": {
      "id": "user-id",
    },
  },
}
`;

exports[`mutation: viewerUpdate only isOfAge is passed 1`] = `
{
  "viewerUpdate": {
    "userDetails": {
      "id": "user-id",
    },
  },
}
`;

exports[`mutation: viewerUpdate only name is passed 1`] = `
{
  "viewerUpdate": {
    "userDetails": {
      "id": "user-id",
    },
  },
}
`;

exports[`mutation: viewerUpdate only path is passed 1`] = `
{
  "viewerUpdate": {
    "userDetails": {
      "id": "user-id",
    },
  },
}
`;

exports[`mutation: viewerUpdate only profile picture is passed 1`] = `
{
  "viewerUpdate": {
    "userDetails": {
      "id": "user-id",
    },
  },
}
`;

exports[`mutation: viewerUpdate profile picture is deleted - set to null 1`] = `
{
  "viewerUpdate": {
    "userDetails": {
      "id": "user-id",
    },
  },
}
`;
