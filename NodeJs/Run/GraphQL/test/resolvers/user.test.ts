import { fullSubscription, subscribeRequest, testContext, user, userDetails } from './test-utils'
import { resolvers } from '../../src/resolvers'
import { GraphQLResolveInfo } from 'graphql/type'
import { GraphQLError } from 'graphql/error'
import { SubscribeRequestState } from '../../src/generated/resolvers-types'

describe('user resolvers', () => {
    describe('field: subscription', () => {
        test('should return users subscription for given creator', async () => {
            const context = testContext({ userId: 'user-id' })
            const subscription = fullSubscription()
            context.dataSources.subscriptionAPI.getSubscription = jest.fn(async () => subscription)

            const result = await resolvers.User!.subscription!(
                user('creator-id'),
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(subscription)
            expect(context.dataSources.subscriptionAPI.getSubscription).toHaveBeenCalledWith('user-id', 'creator-id')
        })

        test('should return null because user is not subscribed to the creator', async () => {
            const context = testContext({ userId: 'user-id' })
            context.dataSources.subscriptionAPI.getSubscription = jest.fn().mockRejectedValue(
                new GraphQLError('Not found', {
                    extensions: {
                        response: {
                            status: 404,
                        },
                    },
                })
            )

            const result = await resolvers.User!.subscription!(
                user('creator-id'),
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toBeNull()
            expect(context.dataSources.subscriptionAPI.getSubscription).toHaveBeenCalledWith('user-id', 'creator-id')
        })

        test('should return null if unauthenticated', async () => {
            const context = testContext()
            context.dataSources.subscriptionAPI.getSubscription = jest.fn()

            const result = await resolvers.User!.subscription!(
                user('creator-id'),
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toBeNull()
            expect(context.dataSources.subscriptionAPI.getSubscription).not.toHaveBeenCalled()
        })
    })

    describe('field: subscriber', () => {
        test('should return subscription that user has to requester', async () => {
            const context = testContext({ userId: 'requester-id' })
            const subscription = fullSubscription()
            context.dataSources.subscriptionAPI.getSubscriber = jest.fn(async () => subscription)

            const result = await resolvers.User!.subscriber!(user('creator-id'), {}, context, {} as GraphQLResolveInfo)

            expect(result).toEqual(subscription)
            expect(context.dataSources.subscriptionAPI.getSubscriber).toHaveBeenCalledWith('requester-id', 'creator-id')
        })

        test('should return null because target user does not subscribe the requester', async () => {
            const context = testContext({ userId: 'requester-id' })
            context.dataSources.subscriptionAPI.getSubscriber = jest.fn().mockRejectedValue(
                new GraphQLError('Not found', {
                    extensions: {
                        response: {
                            status: 404,
                        },
                    },
                })
            )

            const result = await resolvers.User!.subscriber!(user('creator-id'), {}, context, {} as GraphQLResolveInfo)

            expect(result).toBeNull()
            expect(context.dataSources.subscriptionAPI.getSubscriber).toHaveBeenCalledWith('requester-id', 'creator-id')
        })

        test('should return null if unauthenticated', async () => {
            const context = testContext()
            context.dataSources.subscriptionAPI.getSubscriber = jest.fn()

            const result = await resolvers.User!.subscriber!(user('creator-id'), {}, context, {} as GraphQLResolveInfo)

            expect(result).toBeNull()
            expect(context.dataSources.subscriptionAPI.getSubscriber).not.toHaveBeenCalled()
        })
    })

    describe('field: analytics', () => {
        test('analytics are missing from the model, then default empty should be returned', async () => {
            const analytics = await resolvers.User!.analytics!(
                user('creator-id'),
                {},
                testContext(),
                {} as GraphQLResolveInfo
            )

            expect(analytics).toEqual({})
        })

        test('should return analytics from the model', async () => {
            const analytics = await resolvers.User!.analytics!(
                user('creator-id', { facebookPixelId: 'facebook-pixel-id' }),
                {},
                testContext(),
                {} as GraphQLResolveInfo
            )

            expect(analytics).toEqual({ facebookPixelId: 'facebook-pixel-id' })
        })
    })

    describe('field: bio', () => {
        test('should return transformed bio', async () => {
            const bio = await resolvers.User!.bioMarkdown!(
                user('creator-id', undefined, 'bio'),
                {},
                testContext(),
                {} as GraphQLResolveInfo
            )

            expect(bio).toEqual('bio')
        })
    })

    describe('field: spotifyShowId', () => {
        test('should parse spotify show id from the uri', async () => {
            const showId = await resolvers.User!.spotifyShowId!(
                user('creator-id', undefined, 'bio', 'spotify:show:2EzGCIlDADxA4YZUeiaGtN'),
                {},
                testContext(),
                {} as GraphQLResolveInfo
            )

            expect(showId).toEqual('2EzGCIlDADxA4YZUeiaGtN')
        })
    })

    describe('field: subscribeRequestState', () => {
        test('should return PENDING when subscribe request has no acceptedAt or declinedAt', async () => {
            const context = testContext({ userId: 'user-id' })
            const pendingRequest = subscribeRequest({
                userId: 'user-id',
                creatorId: 'creator-id',
                acceptedAt: null,
                declinedAt: null,
            })
            context.dataSources.subscribeRequestAPI.getSubscribeRequest = jest.fn(async () => pendingRequest)
            context.dataSources.subscriptionAPI.getSubscription = jest.fn()

            const result = await resolvers.User!.subscribeRequestState!(
                user('creator-id'),
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(SubscribeRequestState.PENDING)
            expect(context.dataSources.subscribeRequestAPI.getSubscribeRequest).toHaveBeenCalledWith('creator-id')
        })

        test('should return ACCEPTED when subscribe request has acceptedAt', async () => {
            const context = testContext({ userId: 'user-id' })
            const acceptedRequest = subscribeRequest({
                userId: 'user-id',
                creatorId: 'creator-id',
                acceptedAt: '2023-09-18T00:00:00Z',
                declinedAt: null,
            })
            context.dataSources.subscribeRequestAPI.getSubscribeRequest = jest.fn(async () => acceptedRequest)
            context.dataSources.subscriptionAPI.getSubscription = jest.fn()

            const result = await resolvers.User!.subscribeRequestState!(
                user('creator-id'),
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(SubscribeRequestState.ACCEPTED)
            expect(context.dataSources.subscribeRequestAPI.getSubscribeRequest).toHaveBeenCalledWith('creator-id')
        })

        test('should return DECLINED when subscribe request has declinedAt', async () => {
            const context = testContext({ userId: 'user-id' })
            const declinedRequest = subscribeRequest({
                userId: 'user-id',
                creatorId: 'creator-id',
                acceptedAt: null,
                declinedAt: '2023-09-19T00:00:00Z',
            })
            context.dataSources.subscribeRequestAPI.getSubscribeRequest = jest.fn(async () => declinedRequest)
            context.dataSources.subscriptionAPI.getSubscription = jest.fn()

            const result = await resolvers.User!.subscribeRequestState!(
                user('creator-id'),
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(SubscribeRequestState.DECLINED)
            expect(context.dataSources.subscribeRequestAPI.getSubscribeRequest).toHaveBeenCalledWith('creator-id')
        })

        test('should return null when no subscribe request is found', async () => {
            const context = testContext({ userId: 'user-id' })
            context.dataSources.subscribeRequestAPI.getSubscribeRequest = jest.fn(async () => null)
            context.dataSources.subscriptionAPI.getSubscription = jest.fn()

            const result = await resolvers.User!.subscribeRequestState!(
                user('creator-id'),
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toBeNull()
            expect(context.dataSources.subscribeRequestAPI.getSubscribeRequest).toHaveBeenCalledWith('creator-id')
        })

        test('should return null if unauthenticated', async () => {
            const context = testContext()
            context.dataSources.subscribeRequestAPI.getSubscribeRequest = jest.fn()

            const result = await resolvers.User!.subscribeRequestState!(
                user('creator-id'),
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toBeNull()
            expect(context.dataSources.subscribeRequestAPI.getSubscribeRequest).not.toHaveBeenCalled()
        })

        test('should return null when user queries themselves', async () => {
            const context = testContext({ userId: 'user-id' })
            context.dataSources.subscribeRequestAPI.getSubscribeRequest = jest.fn()

            const result = await resolvers.User!.subscribeRequestState!(
                user('user-id'),
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toBeNull()
            expect(context.dataSources.subscribeRequestAPI.getSubscribeRequest).not.toHaveBeenCalled()
        })
    })
})

describe('userDetails resolvers', () => {
    describe('field: bio', () => {
        test('should return transformed bio', async () => {
            const bio = await resolvers.UserDetails!.bioMarkdown!(
                userDetails({ id: 'user-id', bio: 'bio' }),
                {},
                testContext(),
                {} as GraphQLResolveInfo
            )

            expect(bio).toEqual('bio')
        })
    })
})
