import { resolvers } from '../../src/resolvers'
import { messageThread, testContext, user } from './test-utils'
import { GraphQLResolveInfo } from 'graphql/type'

describe('message resolvers', () => {
    describe('field: participants', () => {
        test('should return participants from the model', async () => {
            const context = testContext()
            const messageThreadModel = messageThread('message-thread-id')

            const result = await resolvers.MessageThread!.participants!(
                messageThreadModel,
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(messageThreadModel.participants)
        })

        test('should return participants from the api', async () => {
            const context = testContext()
            const user1 = user('user-1')
            const user2 = user('user-2')
            context.dataSources.userAPI.getUser = jest.fn().mockReturnValueOnce(user1).mockReturnValueOnce(user2)
            const messageThreadModel = messageThread('message-thread-id')
            messageThreadModel.participants = []
            messageThreadModel.participantIds = ['user-1', 'user-2']

            const result = await resolvers.MessageThread!.participants!(
                messageThreadModel,
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual([user1, user2])
            expect(context.dataSources.userAPI.getUser).toHaveBeenNthCalledWith(1, 'user-1')
            expect(context.dataSources.userAPI.getUser).toHaveBeenNthCalledWith(2, 'user-2')
        })
    })
})
