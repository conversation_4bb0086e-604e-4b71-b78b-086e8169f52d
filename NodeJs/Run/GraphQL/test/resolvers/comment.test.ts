import { comment, post, testContext, user } from './test-utils'
import { GraphQLResolveInfo } from 'graphql/type'
import { ParentModel } from '../../src/models/post'
import { resolvers } from '../../src/resolvers'
import { PostVoteType } from '../../src/generated/resolvers-types'

describe('comment resolvers', () => {
    describe('field: user', () => {
        test('API returns not found, should return user stub', async () => {
            const context = testContext()
            context.dataSources.userAPI.getUser = jest.fn().mockRejectedValue('User is not found')

            const result = await resolvers.Comment!.user!(comment(), {}, context, {} as GraphQLResolveInfo)

            expect(result.isDeleted).toBeTruthy()
            expect(context.dataSources.userAPI.getUser).toHaveBeenCalledWith('user-id')
        })

        test('API returns active user', async () => {
            const context = testContext()
            const expectedUser = user('user-id')
            context.dataSources.userAPI.getUser = jest.fn(async () => expectedUser)

            const result = await resolvers.Comment!.user!(
                comment({ userId: 'user-id' }),
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(expectedUser)
            expect(context.dataSources.userAPI.getUser).toHaveBeenCalledWith('user-id')
        })
    })

    describe('field: parent', () => {
        test('returns parent from the model', async () => {
            const context = testContext()
            const parent: ParentModel = {
                ...comment({ id: 'parent-id' }),
                type: 'comment',
            }

            context.dataSources.postAPI.getComment = jest.fn()

            const result = await resolvers.Comment!.parent!(
                comment({ id: 'comment-id', parent }),
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(parent)
            expect(context.dataSources.postAPI.getComment).not.toHaveBeenCalled()
        })

        test('fetches parent from api if not present', async () => {
            const context = testContext()
            const parent: ParentModel = {
                ...comment({ id: 'parent-id' }),
                type: 'comment',
            }
            const expectedComment = {
                ...comment({ id: 'comment-id' }),
                parent: parent,
            }

            context.dataSources.postAPI.getComment = jest.fn(async () => expectedComment)

            const result = await resolvers.Comment!.parent!(
                comment({ id: 'comment-id' }),
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(parent)
            expect(context.dataSources.postAPI.getComment).toHaveBeenCalledWith('comment-id')
        })
    })

    describe('field: textMarkdown', () => {
        test('should return the text', async () => {
            const context = testContext()

            const result = await resolvers.Comment!.textMarkdown!(
                comment({ text: 'textik', textHtml: '<h1>textik</h1>' }),
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual('textik')
        })
    })

    describe('field: myVote', () => {
        test.each<{
            voteValue: number
            expectedVoteType: PostVoteType
        }>([
            {
                voteValue: -1,
                expectedVoteType: PostVoteType.DOWN,
            },
            {
                voteValue: 0,
                expectedVoteType: PostVoteType.NONE,
            },
            {
                voteValue: 1,
                expectedVoteType: PostVoteType.UP,
            },
        ])('%s', async ({ voteValue, expectedVoteType }) => {
            const context = testContext()

            const result = await resolvers.CompleteContentPost!.myVote!(
                post({ myVote: voteValue }),
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(expectedVoteType)
        })
    })
})
