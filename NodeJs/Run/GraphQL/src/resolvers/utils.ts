import { UserModel } from '../models/user'
import { Currency, UserProfileType } from '../generated/resolvers-types'
import { GraphQLError } from 'graphql/error'

export function deletedUser(userId: string): UserModel {
    return {
        id: userId,
        isDeleted: true,
        bio: '',
        counts: {
            posts: 0,
            supporters: 0,
            supportersThreshold: undefined,
            supporting: 0,
            ownedCommunities: 0,
        },
        tier: {
            id: 'EUR05',
            default: false,
            hidden: false,
            priceCents: 500,
            currency: Currency.EUR,
        },
        categories: [],
        name: '',
        hasRssFeed: false,
        path: '',
        subscribable: false,
        verified: false,
        privacyPolicyEnabled: false,
        hasGiftsAllowed: false,
        profileType: UserProfileType.PUBLIC,
    }
}

export function getCodeFromExtensions(extensions: ErrorResponse): number | undefined {
    return extensions.response?.status
}

export function getBodyFromExtensions(extensions: ErrorResponse): unknown {
    return extensions.response?.body
}

export type ErrorResponse = {
    response?: {
        status?: number
        body?: unknown
    }
}

export function unauthorizedError() {
    return new GraphQLError('Unauthorized', {
        extensions: {
            code: 'UNAUTHORIZED',
        },
    })
}

export function validateUserData(data: { cookieExpiration: Date } | undefined) {
    if (!data) {
        throw unauthorizedError()
    }

    if (data.cookieExpiration < new Date()) {
        throw unauthorizedError()
    }
}
