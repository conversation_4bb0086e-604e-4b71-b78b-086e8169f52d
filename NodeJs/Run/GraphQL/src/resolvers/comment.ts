import { CommentParentResolvers, CommentResolvers, PostVoteType } from '../generated/resolvers-types'
import { deletedUser } from './utils'
import { plainTextToMarkdown } from '../common/util'

export const commentResolvers: CommentResolvers = {
    user: async ({ userId }, _, { dataSources }) => {
        try {
            return await dataSources.userAPI.getUser(userId)
        } catch (_e) {
            return deletedUser(userId)
        }
    },

    parent: async ({ id, parent }, __, { dataSources }) => {
        if (parent) {
            return parent
        }
        const comment = await dataSources.postAPI.getComment(id)
        if (!comment.parent) {
            throw new Error(`Failed to resolve parent for comment ${id}`)
        }
        return comment.parent
    },

    textMarkdown: ({ text }) => {
        if (!text) return null

        return plainTextToMarkdown(text)
    },

    myVote: ({ myVote }) => {
        switch (myVote) {
            case 1:
                return PostVoteType.UP
            case 0:
                return PostVoteType.NONE
            case -1:
                return PostVoteType.DOWN
            default:
                throw new Error(`Unknown vote ${myVote}`)
        }
    },
}

export const commentParentResolver: CommentParentResolvers = {
    __resolveType: (parent) => {
        if (parent.type == 'comment') {
            return 'Comment'
        } else {
            return parent.fullAssets ? 'CompleteContentPost' : 'LimitedContentPost'
        }
    },
}
