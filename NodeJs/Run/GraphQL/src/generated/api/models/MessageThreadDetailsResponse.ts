/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { SubscriptionRelationType } from './SubscriptionRelationType'
import type { UserResponse } from './UserResponse'

export type MessageThreadDetailsResponse = {
    id: string;
    participants: Array<UserResponse>;
    participantIds: Array<string>;
    createdAt?: string | null;
    canPost: boolean;
    relation: SubscriptionRelationType;
    commonCreators: Array<string>;
    seenAt?: string | null;
    checkedAt?: string | null;
    lastMessageAt?: string | null;
    lastMessageId?: string | null;
    deleted: boolean;
    archived: boolean;
    deletedAt?: string | null;
};

