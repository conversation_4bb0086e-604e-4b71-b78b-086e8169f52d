import { DataSourceConfig } from '@apollo/datasource-rest'
import { Environment } from '../common/environment'
import { ServiceDataSource } from './ServiceDataSource'
import { PaginationModel } from '../models/pagination'
import { PaginationParams, paginationParamsToQueryParams } from './common-utils'
import {
    CreateSubscribeRequestRequest,
    GetSubscribeRequestResponse,
    PagedSubscribeRequestResponse,
    UpdateSubscribeRequest,
    UpdateSubscribeRequestType,
} from '../generated/api'
import { SubscribeRequestModel } from '../models/subscription'

export class SubscribeRequestAPI extends ServiceDataSource {
    constructor(environment: Environment, cookies?: string, config?: DataSourceConfig) {
        super(environment, 'api', cookies, config)
    }

    async createSubscribeRequest(creatorId: string): Promise<void> {
        const body: CreateSubscribeRequestRequest = {
            creatorId,
        }

        await this.post(`/v1/subscribe-requests`, {
            body,
        })
    }

    async updateSubscribeRequest(id: number, type: UpdateSubscribeRequestType): Promise<void> {
        const body: UpdateSubscribeRequest = {
            type,
        }

        await this.put(`/v1/subscribe-requests/${id}`, {
            body,
        })
    }

    async getSubscribeRequest(creatorId: string): Promise<SubscribeRequestModel | null> {
        const data = (await this.get<GetSubscribeRequestResponse>(`/v1/subscribe-requests/${creatorId}`)).data
        if (data) {
            return {
                id: data.id,
                userId: data.userId,
                creatorId: data.creatorId,
                createdAt: data.createdAt,
                acceptedAt: data.acceptedAt,
                declinedAt: data.declinedAt,
                deletedAt: data.deletedAt,
                seenAt: data.seenAt,
            }
        } else {
            return null
        }
    }

    async getSubscribeRequests(
        paginationParams: PaginationParams
    ): Promise<{ subscribeRequests: SubscribeRequestModel[]; pagination: PaginationModel }> {
        const params = paginationParamsToQueryParams(paginationParams)

        const response = await this.get<PagedSubscribeRequestResponse>(`/v1/subscribe-requests`, {
            params,
        })

        const mappedSubscribeRequests = response.content.map((subscribeRequest) => {
            return {
                id: subscribeRequest.id,
                userId: subscribeRequest.userId,
                creatorId: subscribeRequest.creatorId,
                createdAt: subscribeRequest.createdAt,
                acceptedAt: subscribeRequest.acceptedAt,
                declinedAt: subscribeRequest.declinedAt,
                deletedAt: subscribeRequest.deletedAt,
                seenAt: subscribeRequest.seenAt,
            }
        })

        return {
            subscribeRequests: mappedSubscribeRequests,
            pagination: {
                endCursor: response.afterCursor,
                hasNextPage: response.hasNext,
            },
        }
    }

    async markAllSeen(userId: string) {
        await this.post(`/v1/users/${userId}/mark-subscribe-requests-seen`)
    }
}
