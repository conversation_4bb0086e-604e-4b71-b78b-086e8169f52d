import { DataSourceConfig } from '@apollo/datasource-rest'
import { ApolloServerErrorCode } from '@apollo/server/errors'
import {
    CastVotesRequest,
    CommentResponse,
    CreateCommentRequest,
    CreatePostRequest,
    CreatorPostsSortingFields,
    Direction,
    PagedPostResponse,
    PollResponse,
    PostAssetInput as PostAssetInputRest,
    PostDtoListResponse,
    PostFilterType as RestPostFilterType,
    PostResponse,
    PostVoteRequest,
    SearchPostsRequest,
    UpdateCommentRequest,
    UpdatePollRequest,
    UpdatePostRequest,
} from '../generated/api'
import { ServiceDataSource } from './ServiceDataSource'
import { Environment } from '../common/environment'
import { CommentModel, ParentModel, PollModel, PostModel } from '../models/post'
import { mapDocumentTypeToRest, mapJsonApiToPost, mapToComment, mapToPoll, mapToPost } from './common-mappers'
import { PaginationModel } from '../models/pagination'
import { PaginationParams, paginationParamsToQueryParams } from './common-utils'
import {
    CommentAttributesInput,
    PostAssetInput,
    PostCreateInput,
    PostSortFields,
    PostTypeFilter,
    SortDirection,
} from '../generated/resolvers-types'
import { GraphQLError } from 'graphql/error'

export class PostAPI extends ServiceDataSource {
    constructor(environment: Environment, cookies?: string, config?: DataSourceConfig) {
        super(environment, 'api', cookies, config)
    }

    async getPosts(
        creatorId: string | undefined,
        paginationParams: PaginationParams,
        sort: {
            by?: PostSortFields | null
            order?: SortDirection | null
        },
        filter: {
            categoryId?: string | null
            type?: PostTypeFilter | undefined | null
            term?: string | null
            excludedCreatorIds?: string[] | null
            communityId?: string | null
        }
    ): Promise<{ posts: PostModel[]; pagination: PaginationModel }> {
        const request: SearchPostsRequest = {
            sortBy: mapSortBy(sort.by),
            filter: {
                type: mapType(filter.type),
                categoryId: filter.categoryId,
                query: filter.term,
                communityId: filter.communityId,
                excludedCreatorIds: filter.excludedCreatorIds ?? [],
            },
            pageSize: paginationParams.first,
            creatorId: creatorId,
            sortDirection: mapDirection(sort.order),
            afterCursor: paginationParams.after,
            beforeCursor: paginationParams.before,
        }
        const response = await this.post<PagedPostResponse>(`/v3/posts/search`, { body: request })
        const posts = response.content.map(mapToPost)

        return {
            posts,
            pagination: {
                hasNextPage: response.hasNext,
                endCursor: response.afterCursor,
                startCursor: response.beforeCursor,
            },
        }
    }

    async getPostsJsonAPI(queries: { query?: string }): Promise<{ posts: PostModel[]; pagination: PaginationModel }> {
        const params = new URLSearchParams()
        if (queries.query) {
            params.append('query', queries.query)
        }

        const response = await this.get<PostDtoListResponse>(`/v2/posts`, { params })
        const posts = response.posts.map(mapJsonApiToPost)

        return {
            posts,
            pagination: {
                hasNextPage: response.meta.hasNext,
            },
        }
    }

    async getLivestreams(creatorId: string): Promise<{ posts: PostModel[]; pagination: PaginationModel }> {
        const response = await this.get<PagedPostResponse>(`/v1/users/${creatorId}/livestreams`)
        const posts = response.content.map(mapToPost)

        return {
            posts,
            pagination: {
                hasNextPage: response.hasNext,
                endCursor: response.afterCursor,
            },
        }
    }

    async getPost(postId: string): Promise<PostModel> {
        const response = await this.get<PostResponse>(`/v4/posts/${postId}`)

        return mapToPost(response)
    }

    async getPoll(pollId: string): Promise<PollModel> {
        const response = await this.get<PollResponse>(`/v1/polls/${pollId}`)

        return mapToPoll(response)
    }

    async postVotes(pollId: string, votes: string[]) {
        const request: CastVotesRequest = {
            votes: votes.map((vote) => ({ optionId: vote })),
        }
        await this.post(`/v1/polls/${pollId}/votes`, { body: request })
    }

    async putPoll(pollId: string) {
        const request: UpdatePollRequest = {
            hasEnded: true,
        }
        await this.put(`/v1/polls/${pollId}`, { body: request })
    }

    async getComments(
        targetId: string,
        paginationParams: PaginationParams
    ): Promise<{ comments: CommentModel[]; pagination: PaginationModel }> {
        const params = paginationParamsToQueryParams(paginationParams)
        const response = await this.get<PagedPostResponse>(`/v4/posts/${targetId}/comments`, { params })
        const comments = response.content.map(mapToComment)

        return {
            comments,
            pagination: {
                hasNextPage: response.hasNext,
                startCursor: response.beforeCursor,
                endCursor: response.afterCursor,
            },
        }
    }

    async getComment(commentId: string): Promise<CommentModel> {
        const response = await this.get<CommentResponse>(`/v1/comments/${commentId}`)

        let parent: ParentModel
        if (!response.parent.relationships.parentId) {
            parent = {
                ...mapToPost(response.parent),
                type: 'post',
            }
        } else {
            parent = {
                ...mapToComment(response.parent),
                type: 'comment',
            }
        }

        return {
            ...mapToComment(response.comment),
            parent: parent,
            post: mapToPost(response.rootParent),
            siblingId: response.comment.relationships.siblingId,
            parentId: response.parent.id,
        }
    }

    async createComment(
        parentId: string,
        attributes: CommentAttributesInput,
        siblingId?: string
    ): Promise<CommentModel> {
        const body: CreateCommentRequest = {
            parentId,
            siblingId,
            attributes: {
                ...attributes,
                assets: this.mapAssets(attributes.assets),
            },
        }
        const response = await this.post<PostResponse>('/v1/comments', {
            body: body,
        })

        return mapToComment(response)
    }

    async updateComment(commentId: string, attributes: CommentAttributesInput): Promise<CommentModel> {
        const body: UpdateCommentRequest = {
            attributes: {
                ...attributes,
                assets: this.mapAssets(attributes.assets),
            },
        }
        const response = await this.put<PostResponse>(`/v1/comments/${commentId}`, {
            body: body,
        })

        return mapToComment(response)
    }

    async deleteComment(commentId: string) {
        await this.delete(`/v1/posts/${commentId}`)
    }

    async deletePost(postId: string) {
        await this.delete(`/v1/posts/${postId}`)
    }

    async createPost(attributes: PostCreateInput) {
        const body: CreatePostRequest = {
            attributes: {
                assets: this.mapAssets(attributes.assets),
                text: attributes.text,
                textHtml: attributes.textHtml,
                textDelta: attributes.textDelta,
            },
            isAgeRestricted: attributes.isAgeRestricted ?? false,
            isSponsored: attributes.isSponsored ?? false,
            categories: attributes.categories,
            communityId: attributes.communityId,
            publishedAt:
                attributes.publishedAt instanceof Date ? attributes.publishedAt.toISOString() : attributes.publishedAt,
        }
        const response = await this.post<PostResponse>(`/v4/posts`, {
            body: body,
        })

        return mapToPost(response)
    }

    async updatePost(
        postId: string,
        attributes: {
            assets: PostAssetInput[]
            text: string
            textHtml: string
            textDelta: string | undefined
            categories: string[]
            publishedAt: string | Date | undefined
            pinnedAt: string | Date | undefined
            isSponsored: boolean
            isAgeRestricted: boolean
            isExcludedFromRss: boolean
        }
    ) {
        const body: UpdatePostRequest = {
            attributes: {
                assets: this.mapAssets(attributes.assets),
                text: attributes.text,
                textHtml: attributes.textHtml,
                textDelta: attributes.textDelta,
            },
            isAgeRestricted: attributes.isAgeRestricted,
            isSponsored: attributes.isSponsored,
            categories: attributes.categories,
            publishedAt:
                attributes.publishedAt instanceof Date ? attributes.publishedAt.toISOString() : attributes.publishedAt,
            pinnedAt: attributes.pinnedAt instanceof Date ? attributes.pinnedAt.toISOString() : attributes.pinnedAt,
            excludeFromRss: attributes.isExcludedFromRss,
        }
        const response = await this.put<PostResponse>(`/v4/posts/${postId}`, {
            body: body,
        })

        return mapToPost(response)
    }

    async postPostVotes(postId: string, voteValue: number) {
        const body: PostVoteRequest = {
            voteValue,
        }

        await this.post(`/v4/posts/${postId}/votes`, {
            body: body,
        })
    }

    private mapAssets(inputAssets: PostAssetInput[]): PostAssetInputRest[] {
        return inputAssets.map((asset) => {
            validateAsset(asset)
            if (asset.image) {
                return {
                    image: {
                        ...asset.image,
                        hidden: false,
                    },
                }
            } else if (asset.document) {
                return {
                    document: {
                        name: asset.document.name,
                        url: asset.document.url,
                        type: mapDocumentTypeToRest(asset.document.type),
                    },
                    thumbnail: asset.document.thumbnailUrl,
                    thumbnailImage:
                        (asset.document.thumbnail && {
                            ...asset.document.thumbnail,
                            hidden: false,
                        }) ??
                        undefined,
                }
            } else if (asset.gjirafa) {
                return {
                    gjirafa: {
                        id: asset.gjirafa.id,
                    },
                    thumbnail: asset.gjirafa.thumbnailUrl,
                    thumbnailImage:
                        (asset.gjirafa.thumbnail && {
                            ...asset.gjirafa.thumbnail,
                            hidden: false,
                        }) ??
                        undefined,
                }
            } else if (asset.gjirafaLivestream) {
                return {
                    gjirafaLivestream: {
                        id: asset.gjirafaLivestream.id,
                    },
                    thumbnail: asset.gjirafaLivestream.thumbnailUrl,
                    thumbnailImage:
                        (asset.gjirafaLivestream.thumbnail && {
                            ...asset.gjirafaLivestream.thumbnail,
                            hidden: false,
                        }) ??
                        undefined,
                }
            } else {
                throw Error(`Failed to map asset ${JSON.stringify(asset)}`)
            }
        })
    }
}

function mapSortBy(by: PostSortFields | null | undefined): CreatorPostsSortingFields | undefined {
    switch (by) {
        case PostSortFields.PINNED_AT:
            return CreatorPostsSortingFields.PINNED_AT
        case PostSortFields.PUBLISHED_AT:
            return CreatorPostsSortingFields.PUBLISHED_AT
        case PostSortFields.WATCHED_AT:
            return CreatorPostsSortingFields.WATCHED_AT
        case PostSortFields.VIEWS:
            return CreatorPostsSortingFields.VIEWS
        case PostSortFields.TERM_RELEVANCE:
            return CreatorPostsSortingFields.QUERY_SIMILARITY
        case undefined:
        case null:
            return undefined
    }
}

function mapDirection(direction: PaginationParams['sortDirection']): Direction | undefined {
    switch (direction) {
        case 'DESC':
            return Direction.DESC
        case 'ASC':
            return Direction.ASC
    }
}

function mapType(type: PostTypeFilter | undefined | null): RestPostFilterType | undefined {
    switch (type) {
        case PostTypeFilter.IN_PROGRESS:
            return RestPostFilterType.IN_PROGRESS
        case undefined:
        case null:
            return undefined
    }
}

function validateAsset(asset: PostAssetInput) {
    const filledFields = Object.keys(asset).filter((field) => asset[field as keyof PostAssetInput])

    if (filledFields.length === 0) {
        throw new GraphQLError('At least one field must be defined for an asset', {
            extensions: { code: ApolloServerErrorCode.BAD_REQUEST },
        })
    }

    if (filledFields.length > 1) {
        throw new GraphQLError('Only one field can be defined for an asset', {
            extensions: { code: ApolloServerErrorCode.BAD_REQUEST },
        })
    }
}
