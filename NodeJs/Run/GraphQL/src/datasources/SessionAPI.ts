import { DataSourceConfig } from '@apollo/datasource-rest'
import { Environment } from '../common/environment'
import { ServiceDataSource } from './ServiceDataSource'

export class SessionAPI extends ServiceDataSource {
    constructor(environment: Environment, cookies?: string, config?: DataSourceConfig) {
        super(environment, 'auth', cookies, config)
    }

    async deleteSession(sessionId: string): Promise<void> {
        await this.delete(`/v1/sessions/${sessionId}`)
    }

    async deleteSessions(): Promise<void> {
        await this.delete(`/v1/sessions`)
    }
}
