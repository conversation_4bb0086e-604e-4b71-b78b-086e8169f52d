{"name": "graphql", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"compile": "tsc --build", "dev": "ts-node-dev --respawn ./src/index.ts", "test": "jest", "lint": "eslint src test", "check": "npm run lint && npm test --ci --report=default --report=jest-junit", "lint:fix": "eslint src test --fix", "format": "prettier --check src '**/*.{ts,graphql}'", "format:fix": "prettier --write src '**/*.{ts,graphql}'", "start": "npm run compile && node ./dist/index.js", "codegen": "graphql-codegen --config codegen.ts", "generate-api-model": "openapi --input http://localhost:8080/docs/openapi.json --output ./src/generated/api  --exportSchemas false --exportCore false --exportServices false", "generate-gjirafa-model": "openapi --input https://vp-api.gjirafa.tech/swagger/v1/swagger.json --output ./src/generated/gjirafa  --exportSchemas false --exportCore false --exportServices false"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@apollo/datasource-rest": "^6.3.0", "@apollo/server": "^4.11.0", "@escape.tech/graphql-armor": "^3.1.0", "@google-cloud/logging-winston": "^6.0.0", "@graphql-hive/apollo": "^0.36.2", "@graphql-tools/merge": "^9.0.8", "@graphql-tools/schema": "^10.0.7", "@sentry/node": "^8.35.0", "@types/linkify-it": "^5.0.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.1", "graphql": "^16.9.0", "graphql-parse-resolve-info": "^4.13.0", "graphql-scalars": "^1.23.0", "linkify-it": "^5.0.0", "ts-pattern": "^5.5.0", "turndown": "^7.2.0", "winston": "^3.15.0", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.13.0", "@graphql-codegen/cli": "5.0.3", "@graphql-codegen/typescript": "4.1.0", "@graphql-codegen/typescript-resolvers": "4.3.0", "@graphql-hive/cli": "^0.44.2", "@types/cors": "^2.8.17", "@types/jest": "^29.5.14", "@types/node": "^22.7.9", "@types/turndown": "^5.0.5", "@typescript-eslint/eslint-plugin": "^8.11.0", "@typescript-eslint/parser": "^8.11.0", "eslint": "^9.13.0", "jest": "^29.7.0", "jest-junit": "^16.0.0", "nock": "^13.5.5", "openapi-typescript-codegen": "^0.29.0", "prettier": "^3.3.3", "ts-jest": "^29.2.5", "ts-node-dev": "^2.0.0", "typescript": "^5.6.3", "typescript-eslint": "^8.11.0"}}