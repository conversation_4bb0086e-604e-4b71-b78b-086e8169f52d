include:
  - "/NodeJs/Run/GraphQL/.gitlab-ci.yml"
  - "/NodeJs/Run/TagManager/.gitlab-ci.yml"

.NodeJs/Run/build-service:
  stage: build-services
  extends:
    - .run-always
  variables:
    SERVICE_TYPE: cloud_run
  artifacts:
    reports:
      junit: 'NodeJs/Run/GraphQL/junit.xml'

.NodeJs/Run/build-service-script:
  script:
    - if [ -z "${SERVICE_NAME}" ]; then echo SERVICE_NAME must be set, failing.; exit 1; fi
    - MODULE_HASH=$($CI_PROJECT_DIR/GitLab/module-hash.py $CI_JOB_NAME)
    - export IMAGE_TAG=$IMAGE_BASE/$SERVICE_NAME:$MODULE_HASH
    - echo "image tag is $IMAGE_TAG"
    - GCS_ARTIFACTS_URL=gs://heroheroco-gitlab-build-cache/artifacts-v1/$MODULE_HASH.tar
    # Skip build if cache already exists. Also restore artifacts (test reports, ...) to help Git<PERSON>ab MR reports.
    - if docker manifest inspect $IMAGE_TAG >/dev/null; then
      echo "Image $IMAGE_TAG already in remote repository, skipping its build.";
      echo "To force building the image, run \`gcloud container images delete $IMAGE_TAG\`.";
      exit;
      else
      echo "Building with image $IMAGE_TAG.";
      fi
    - npm ci
    - npm run format
    - npm run check
    - docker build -t $IMAGE_TAG -f ./Dockerfile .
    - docker push $IMAGE_TAG
    - docker manifest inspect $IMAGE_TAG > /dev/null

.nodejs-run-variables: &nodejs-run-variables
  IMAGE_BASE: europe-west1-docker.pkg.dev/${CLOUD_PROJECT}/cloud-run

.NodeJs/Run/variables-devel:
  extends:
    - .variables-devel
  variables:
    <<: *nodejs-run-variables
    MEMORY: 512Mi
    CPU: 1
    MIN_INSTANCES: 1
    MAX_INSTANCES: 2
    NODE_ENV: devel

.NodeJs/Run/deploy-devel:
  extends:
    - .NodeJs/Run/variables-devel
    - .NodeJs/Run/deploy
    - .run-always-on-main-or-manually

.NodeJs/Run/variables-staging:
  extends:
    - .variables-staging
  variables:
    <<: *nodejs-run-variables
    MEMORY: 512Mi
    CPU: 1
    MIN_INSTANCES: 1
    MAX_INSTANCES: 2
    NODE_ENV: staging

.NodeJs/Run/deploy-staging:
  extends:
    - .NodeJs/Run/variables-staging
    - .NodeJs/Run/deploy
    - .run-always-on-main-or-manually

.NodeJs/Run/variables-prod:
  extends:
    - .variables-prod
  variables:
    <<: *nodejs-run-variables
    MEMORY: 1024Mi
    CPU: 1
    MIN_INSTANCES: 1
    MAX_INSTANCES: 10
    NODE_ENV: production

.NodeJs/Run/deploy-prod:
  extends:
    - .NodeJs/Run/variables-prod
    - .NodeJs/Run/deploy
    - .run-only-manually

.NodeJs/Run/deploy:
  environment: $SERVICE_NAME/$ENVIRONMENT
  script:
    - !reference [ .NodeJs/Run/deploy-script, script ]

.NodeJs/Run/deploy-script:
  script:
    - MODULE_HASH=$($CI_PROJECT_DIR/GitLab/module-hash.py ${CI_JOB_NAME%/*}/build)
    - export IMAGE_TAG=$IMAGE_BASE/$SERVICE_NAME:$MODULE_HASH
    - docker manifest inspect $IMAGE_TAG > /dev/null  # safeguard: this fails if computed image tag does not exist
    - export CURRENT_IMAGE_TAG=`gcloud run services describe $ENVIRONMENT-$SERVICE_NAME --format='value(spec.template.spec.containers.image)' --platform=managed --region=$CLOUD_REGION`
    - if [ "$CURRENT_IMAGE_TAG" == "$IMAGE_TAG" ]; then
      echo "Image $IMAGE_TAG already deployed, skipping deployment.";
      gcloud run services list --platform managed;
      exit;
      else
      echo "Replacing $CURRENT_IMAGE_TAG with $IMAGE_TAG.";
      fi
    # traffic-heavy services will run cheaper if running 24/7 without throttling
    - if [ "$THROTTLING" = "no" ]; then
      THROTTLING="--no-cpu-throttling";
      else
      THROTTLING="--cpu-throttling";
      fi
    - gcloud beta run deploy "$ENVIRONMENT-$SERVICE_NAME"
      --image $IMAGE_TAG
      --platform managed
      --min-instances $MIN_INSTANCES
      --max-instances $MAX_INSTANCES
      --cpu-boost
      $THROTTLING
      --cpu $CPU
      --memory $MEMORY
      --region $CLOUD_REGION
      --service-account <EMAIL>
      --allow-unauthenticated
      --clear-vpc-connector
      --vpc-egress private-ranges-only
      --no-traffic
      --labels environment=$ENVIRONMENT
      --set-env-vars=SERVICE_NAME=$SERVICE_NAME
      --set-env-vars=SERVICE_TYPE=cloud_run
      --set-env-vars=MODULE_HASH=$MODULE_HASH
      --set-env-vars=ENVIRONMENT=$ENVIRONMENT
      --set-env-vars=PRODUCTION=$PRODUCTION
      --set-env-vars=CLOUD_PROJECT=$CLOUD_PROJECT
      --set-env-vars=CLOUD_REGION=$CLOUD_REGION
      --set-env-vars=HOSTNAME=$HOSTNAME
      --set-env-vars=LOG_APPENDER=ConsoleFluentD
      --set-env-vars=NODE_ENV=$NODE_ENV
      --set-env-vars=SENTRY_DSN=$SENTRY_DSN
      --set-env-vars=GJIRAFA_PROJECT=$GJIRAFA_PROJECT
      --set-env-vars=GJIRAFA_API_KEY=$GJIRAFA_API_KEY
      --set-env-vars=^##^GJIRAFA_IMAGE_KEY="$GJIRAFA_IMAGE_KEY"
      --set-env-vars=ORIGIN_POLICY=$ORIGIN_POLICY
      --set-env-vars=INTERNAL_API_KEY=$INTERNAL_API_KEY
      --set-env-vars=$ENV_VARS
    - gcloud run services list --platform managed
    # To avoid cold start with 429 too many requests, we route slowly new traffic.
    - sleep 1
    - gcloud run services update-traffic "$ENVIRONMENT-$SERVICE_NAME" --platform managed --region $CLOUD_REGION --to-revisions=LATEST=10
    - sleep 10
    - gcloud run services update-traffic "$ENVIRONMENT-$SERVICE_NAME" --platform managed --region $CLOUD_REGION --to-revisions=LATEST=30
    - sleep 10
    - gcloud run services update-traffic "$ENVIRONMENT-$SERVICE_NAME" --platform managed --region $CLOUD_REGION --to-revisions=LATEST=100
