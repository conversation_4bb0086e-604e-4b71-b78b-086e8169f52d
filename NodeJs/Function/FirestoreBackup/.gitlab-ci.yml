.NodeJs/Function/FirestoreBackup/variables:
  variables:
    FUNCTION_NAME: cronjob-firestore-backup
    ENVIRONMENT_FUNCTION_NAME: generic-cronjob-firestore-backup
    TOPIC: "Daily"
    MEMORY: 128Mi
    BACKUP_BUCKET: "gs://heroheroco-firestore-backup"
    TRIGGER: "--trigger-topic=$TOPIC_NAME"
    ENV_VARS: "BACKUP_BUCKET=$BACKUP_BUCKET"
    SERVICE_ACCOUNT: "<EMAIL>"

# Run these to get correct permissions:
NodeJs/Function/FirestoreBackup/deploy-prod:
  extends:
    - .NodeJs/Function/deploy
    - .NodeJs/Function/FirestoreBackup/variables
    - .Function/variables-prod
  stage: deploy-prod
  when: manual

NodeJs/Function/FirestoreBackup/setup-iam:
  stage: deploy-prod
  when: manual
  needs: []
  extends:
    - .NodeJs/Function/FirestoreBackup/variables
    - .Function/variables-prod
  script:
    # TODO why gitlab-runner service account is not used?
    - gcloud config list
    - gcloud projects add-iam-policy-binding heroheroco --member serviceAccount:$SERVICE_ACCOUNT --role roles/datastore.importExportAdmin
    - gsutil iam ch serviceAccount:<EMAIL>:admin gs://heroheroco-firestore-backup
