ENVIRONMENT=local
PRODUCTION=false
CLOUD_PROJECT=heroheroco
LOG_APPENDER=ConsoleOut
GOOGLE_APPLICATION_CREDENTIALS=/path/to/home/<USER>/gcloud/service-account.json
HOSTNAME=https://loc.herohero.co
HOSTNAME_SERVICES=https://loc.herohero.co
TF_VAR_FASTLY_TERRAFORM_TOKEN=********************************
SERVICE_TYPE=cloud_function
CLOUD_REGION=europe
IO_RETRIES=0
SQL_PROXY_UNIX_SOCKET=/path/to/socket/.cloudsql/heroheroco:europe-west1:postgresql-instance/.s.PGSQL.5432
SQL_INSTANCE_NAME=heroheroco:europe-west1:postgresql-instance
SQL_DATABASE_NAME=local-herohero-db
INTERNAL_API_KEY=internal_api_key
GJIRAFA_API_KEY=
GJIRAFA_IMAGE_KEY=
GJIRAFA_PROJECT=agmipoda
GJIRAFA_API_KEY_PROD=
GJIRAFA_IMAGE_KEY_PROD=
GJIRAFA_PROJECT_PROD=agmipobm
CONNECT_SECRET_KEY=
STRIPE_API_KEY_EU=
STRIPE_API_KEY_US=
