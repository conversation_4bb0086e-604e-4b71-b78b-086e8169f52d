# Signoz instance is unfortunately mis-named in GCP and ideally we should convert it to
# Kubernetes as it requires several containers running. Keeping it here for reference
# and future readers.

# Warn: there is a committed-use-discount for e2-standard-2 in europe-west1-b:
# - https://console.cloud.google.com/compute/commitments/details/europe-west1/e2-standard-2-commitment-2025?project=heroheroco
# In case of RECREATING this instance, make sure to use the SAME machine-type.

resource "google_compute_instance" "signoz_instance" {
  tags                       = ["https-server"]
  project                    = "heroheroco"
  zone                       = "europe-west1-b"
  key_revocation_action_type = "NONE"
  machine_type               = "e2-standard-2"
  name                       = "general-purpose"
  metadata                   = {}

  lifecycle {
    ignore_changes = [metadata]
  }

  boot_disk {
    auto_delete = true
    device_name = "instance-20250415-112943"
    initialize_params {
      image = "https://www.googleapis.com/compute/beta/projects/debian-cloud/global/images/debian-12-bookworm-v20250311"
      size  = 100
      type  = "pd-balanced"
    }
    mode   = "READ_WRITE"
    source = "https://www.googleapis.com/compute/v1/projects/heroheroco/zones/europe-west1-b/disks/general-purpose"
  }

  confidential_instance_config {
    enable_confidential_compute = false
  }

  network_interface {
    access_config {
      nat_ip       = "*************"
      network_tier = "PREMIUM"
    }
    network            = "https://www.googleapis.com/compute/v1/projects/heroheroco/global/networks/default"
    network_ip         = "************"
    stack_type         = "IPV4_ONLY"
    subnetwork         = "https://www.googleapis.com/compute/v1/projects/heroheroco/regions/europe-west1/subnetworks/default"
    subnetwork_project = "heroheroco"
  }

  scheduling {
    automatic_restart   = true
    on_host_maintenance = "MIGRATE"
    provisioning_model  = "STANDARD"
  }

  service_account {
    email = "<EMAIL>"
    scopes = [
      "https://www.googleapis.com/auth/devstorage.read_only",
      "https://www.googleapis.com/auth/logging.write",
      "https://www.googleapis.com/auth/monitoring.write",
      "https://www.googleapis.com/auth/service.management.readonly",
      "https://www.googleapis.com/auth/servicecontrol",
      "https://www.googleapis.com/auth/trace.append",
    ]
  }

  shielded_instance_config {
    enable_integrity_monitoring = true
    enable_secure_boot          = false
    enable_vtpm                 = true
  }
}
