resource "google_sql_database_instance" "postgresql-instance" {
  name             = "postgresql-instance"
  region           = var.region
  database_version = "POSTGRES_15"
  settings {
    tier                        = "db-custom-4-16384"
    deletion_protection_enabled = true

    database_flags {
      name  = "cloudsql.iam_authentication"
      value = "on"
    }

    maintenance_window {
      day          = 1
      hour         = 2
      update_track = "week5"
    }

    ip_configuration {
      ipv4_enabled                                  = true // for now we allow public IP
      private_network                               = google_compute_network.vpc.self_link
      enable_private_path_for_google_cloud_services = true
    }
  }
  deletion_protection = true
}

resource "google_sql_database" "devel-database" {
  name     = "devel-herohero-db"
  instance = google_sql_database_instance.postgresql-instance.name
}

resource "google_sql_database" "staging-database" {
  name     = "staging-herohero-db"
  instance = google_sql_database_instance.postgresql-instance.name
}

resource "google_sql_database" "prod-database" {
  name     = "prod-herohero-db"
  instance = google_sql_database_instance.postgresql-instance.name
}

resource "google_compute_global_address" "private_db_ip_address" {
  name          = "herohero-private-db-ip-address"
  purpose       = "VPC_PEERING"
  address_type  = "INTERNAL"
  prefix_length = 16
  network       = google_compute_network.vpc.id
}

# We should probably be able to delete this, however for now we get:
# > Producer services (e.g. CloudSQL, Cloud Memstore, etc.) are still using this connection.
resource "google_service_networking_connection" "private_vpc_connection" {
  network                 = google_compute_network.vpc.id
  service                 = "servicenetworking.googleapis.com"
  reserved_peering_ranges = [google_compute_global_address.private_db_ip_address.name]
}
