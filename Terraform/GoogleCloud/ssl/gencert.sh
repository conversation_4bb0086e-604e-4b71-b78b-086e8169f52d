#!/bin/bash

FQDN=*.herohero.co
SUBJ="/C=CZ/ST=Czechia/L=Prague/O=Herohero/subjectAltName=DNS:herohero.co/CN=$FQDN"
# max validity is 397 days
# https://stackoverflow.com/questions/64597721
VALIDITY=397

openssl genrsa -out selfsigned.key 2048

openssl req -new -sha256 \
    -out selfsigned.csr \
    -key selfsigned.key \
    -config selfsigned.conf

openssl x509 -req \
    -sha256 \
    -days $VALIDITY \
    -in selfsigned.csr \
    -signkey selfsigned.key \
    -out selfsigned.crt \
    -extensions req_ext \
    -extfile selfsigned.conf
