terraform {
  required_providers {
    stripe = {
      source  = "franckverrot/stripe"
    }
  }
  backend "gcs" {
    bucket = "heroheroco-terraform-state"
    prefix = "stripe/state-devel-us"
  }
}

variable "STRIPE_DEVEL_US_TERRAFORM_TOKEN" {
  type = string
}

provider "stripe" {
  # NOTE: This is populated from the `TF_VAR_STRIPE_###_TERRAFORM_TOKEN` environment variable.
  # The value is duplicitely kept in Firestore:
  # https://console.cloud.google.com/firestore/data/panel/constants/?project=heroheroco
  api_token = var.STRIPE_DEVEL_US_TERRAFORM_TOKEN
}

resource "stripe_webhook_endpoint" "devel_webhook_accounts" {
  url     = "https://svc-devel.herohero.co/stripe/v1/webhooks/accounts?currency=usd"
  connect = true
  enabled_events = [
    "account.external_account.updated",
    "account.external_account.deleted",
    "account.external_account.created",
    "account.updated",
  ]
}

resource "stripe_webhook_endpoint" "devel_webhook_payment_methods" {
  url     = "https://svc-devel.herohero.co/stripe/v1/webhooks/payment-methods?currency=usd"
  connect = false
  enabled_events = [
    "payment_method.attached",
  ]
}

resource "stripe_webhook_endpoint" "devel_webhook_payouts" {
  url     = "https://svc-devel.herohero.co/stripe/v1/webhooks/payouts?currency=usd"
  connect = true
  enabled_events = [
    "payout.paid",
  ]
}

resource "stripe_webhook_endpoint" "devel_webhook_subscriptions" {
  url     = "https://svc-devel.herohero.co/stripe/v1/webhooks/subscriptions?currency=usd"
  connect = false

  enabled_events = [
    "subscription_schedule.aborted",
    "subscription_schedule.canceled",
    "subscription_schedule.completed",
    "subscription_schedule.created",
    "subscription_schedule.expiring",
    "subscription_schedule.released",
    "subscription_schedule.updated",
    "customer.subscription.updated",
    "customer.subscription.deleted",
    "customer.subscription.created",
  ]
}

resource "stripe_webhook_endpoint" "devel_webhook_charges" {
  url     = "https://svc-devel.herohero.co/stripe/v1/webhooks/charges?currency=usd"
  connect = false
  enabled_events = [
    "charge.succeeded",
    "charge.updated",
    "charge.refunded",
    "charge.failed",
  ]
}

resource "stripe_webhook_endpoint" "devel_webhook_coupons" {
  url     = "https://svc-devel.herohero.co/stripe/v1/webhooks/coupons?currency=usd"
  connect = false
  enabled_events = [
    "coupon.created",
    "coupon.deleted",
    "coupon.updated",
  ]
}

resource "stripe_webhook_endpoint" "devel_webhook_customers" {
  url     = "https://svc-devel.herohero.co/stripe/v1/webhooks/customers?currency=usd"
  connect = false
  enabled_events = [
    "customer.created",
    "customer.deleted",
    "customer.updated",
  ]
}

resource "stripe_webhook_endpoint" "devel_webhook_reports" {
  url     = "https://svc-devel.herohero.co/stripe/v1/webhooks/reports?currency=usd"
  connect = false
  enabled_events = [
    "reporting.report_type.updated",
    "reporting.report_run.failed",
    "reporting.report_run.succeeded",
  ]
}

resource "stripe_webhook_endpoint" "devel_webhook_invoices" {
  url     = "https://svc-devel.herohero.co/stripe/v1/webhooks/invoices?currency=usd"
  connect = false
  enabled_events = [
    "invoice.created",
    "invoice.updated",
  ]
}
