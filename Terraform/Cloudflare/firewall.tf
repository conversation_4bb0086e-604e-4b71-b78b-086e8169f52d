resource "cloudflare_ruleset" "firewall" {
  kind    = "zone"
  name    = "default"
  phase   = "http_request_firewall_custom"
  zone_id = cloudflare_zone.herohero_co.id
  rules {
    action = "skip"
    action_parameters {
      phases   = ["http_ratelimit", "http_request_firewall_managed", "http_request_sbfm"]
      products = ["zoneLockdown", "uaBlock", "bic", "hot", "rateLimit", "securityLevel", "waf"]
      ruleset  = "current"
    }
    description = "Stripe and Gjirafa webhooks + feeds pass + robots + known IPs"
    enabled     = true
    expression = join(" or ", [
      // gjirafa webhooks
      "(http.request.uri.path contains \"/v1/gjirafa-webhooks\")",
      // stripe webhooks
      "(http.request.uri.path contains \"/v1/stripe/webhooks\")",
      // rss comes from all over the world and cannot be cloudflare-challenged
      "(http.request.uri.path contains \"/rss-feed\")",
      // same as for rss
      "(http.request.uri.path contains \"/product-feed-generator\")",
      // gjirafa handling endpoints must not fail during upload
      "(http.request.uri.path contains \"/gjirafa-\")",
      // certified bots are also always allowed
      "(cf.client.bot)",
      // rumaan's local api
      "(ip.src in {*************})"
    ])
    logging {
      enabled = true
    }
  }
  rules {
    action      = "managed_challenge"
    description = "Challenge Non-[EU,NA,SA]"
    enabled     = true
    # OPTIONS requests must not be part of managed_challenge, since cf_clearance cookie is not sent with the preflight request
    # https://developers.cloudflare.com/waf/reference/cloudflare-challenges/#cross-origin-resource-sharing-cors-preflight-requests
    # Herohero, HeroHero are used by the apps, private access token seems not to work, so we need this workaround
    expression = "(not ip.geoip.continent in {\"EU\" \"NA\" \"SA\"} and http.request.method ne \"OPTIONS\" and ip.geoip.country ne \"JP\" and not http.user_agent contains \"Herohero\" and not http.user_agent contains \"HeroHero\")"
  }
  rules {
    action      = "managed_challenge"
    description = "Challenge when accessing invoices or reports"
    enabled     = true
    expression = join(" or ", [
      "(http.request.uri.path eq \"/receipt\")",
      "(http.request.uri.path contains \"/invoice-generator\")",
      "(http.request.uri.path contains \"/creator-privacy-policy-generator\")",
      "(http.request.uri.path contains \"/gift-voucher\")",
      "(http.request.uri.path contains \"/invoices/\" and http.request.uri.path contains \"/report\")",
    ])
  }
  rules {
    action      = "block"
    description = "Block non-HTTP ports"
    enabled     = true
    expression  = "not cf.edge.server_port in {80 443}"
  }
}

resource "cloudflare_ruleset" "rate_limitting_rulesets" {
  kind    = "zone"
  name    = "default"
  phase   = "http_ratelimit"
  zone_id = cloudflare_zone.herohero_co.id
  rules {
    action = "block"
    action_parameters {
      response {
        content      = "{}"
        content_type = "application/json"
        status_code  = 429
      }
    }
    description = "Limit access to /v1/challenge"
    enabled     = true
    expression  = "(http.request.uri.path contains \"/challenge\") or (http.request.uri.path contains \"/sign-up\")"
    ratelimit {
      characteristics     = ["ip.src", "cf.colo.id"]
      mitigation_timeout  = 3600
      period              = 60
      requests_per_period = 15
      requests_to_origin  = false
    }
  }
}
