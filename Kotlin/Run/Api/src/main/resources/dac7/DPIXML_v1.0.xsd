<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2021 (x64) (http://www.altova.com) by <PERSON> (OECD) -->
<xsd:schema xmlns:dpi="urn:oecd:ties:dpi:v1" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:stf="urn:oecd:ties:dpistf:v1" xmlns:iso="urn:oecd:ties:isodpitypes:v1" targetNamespace="urn:oecd:ties:dpi:v1" elementFormDefault="qualified" attributeFormDefault="unqualified" version="1.0">
	<xsd:import namespace="urn:oecd:ties:isodpitypes:v1" schemaLocation="isodpitypes_v1.0.xsd"/>
	<xsd:import namespace="urn:oecd:ties:dpistf:v1" schemaLocation="oecddpitypes_v1.0.xsd"/>
	<!--+++++++++++++++++++++++  Reusable Simple types ++++++++++++++++++++++++++++++++++++++ -->
	<!-- Message type definitions -->
	<!--  -->
	<xsd:simpleType name="MessageType_EnumType">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">Message type defines the type of reporting.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="DPI">
				<xsd:annotation>
					<xsd:documentation>This type defines the messages to be exchanged under the OECD Model Rules and [EU Specific] [EU DIR2021/514].</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DAC7">
				<xsd:annotation>
					<xsd:documentation>[EU Specific] The only allowable entry in this field for messages exchanged under [EU DIR2021/514] is “DAC7”.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- MessageTypeIndic - 4 -->
	<xsd:simpleType name="DPIMessageTypeIndic_EnumType">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">The MessageTypeIndic defines the type of message sent.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="DPI401">
				<xsd:annotation>
					<xsd:documentation>The message contains new (including additional) information.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DPI402">
				<xsd:annotation>
					<xsd:documentation>The message contains corrections for previously sent information.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DPI403">
				<xsd:annotation>
					<xsd:documentation>The message advises that there is no data to report.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!--  -->
	<xsd:simpleType name="INType_EnumType">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">The INType defines the type of identification number being sent.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="LEI">
				<xsd:annotation>
					<xsd:documentation>Legal Entity Identifier</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="EIN">
				<xsd:annotation>
					<xsd:documentation>Entity Identification Number</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="IIN">
				<xsd:annotation>
					<xsd:documentation>Individual Identification Number</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="BRN">
				<xsd:annotation>
					<xsd:documentation>Business Registration Number</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="Other">
				<xsd:annotation>
					<xsd:documentation>Other</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!--  -->
	<!--DPI Property Type -->
	<xsd:simpleType name="DPIPropertyType_EnumType">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">Main business activities
			</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="DPI901">
				<xsd:annotation>
					<xsd:documentation>Office</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DPI902">
				<xsd:annotation>
					<xsd:documentation>Hotel room</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DPI903">
				<xsd:annotation>
					<xsd:documentation>Bed and Breakfast room</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DPI904">
				<xsd:annotation>
					<xsd:documentation>House</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DPI905">
				<xsd:annotation>
					<xsd:documentation>Apartment</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DPI906">
				<xsd:annotation>
					<xsd:documentation>Mobile Home</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DPI907">
				<xsd:annotation>
					<xsd:documentation>Campground</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DPI908">
				<xsd:annotation>
					<xsd:documentation>Boat</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DPI909">
				<xsd:annotation>
					<xsd:documentation>Parking Space</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="DPI910">
				<xsd:annotation>
					<xsd:documentation>Other</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!--  -->
	<!--DPI Nexus Type -->
	<xsd:simpleType name="Nexus_EnumType">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">The reason for which the information will be filled to the competent authority of the EU Member State.
			</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="RPONEX1">
				<xsd:annotation>
					<xsd:documentation>The Reporting Platform Operator is resident for tax purposes in the EU Member State.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RPONEX2">
				<xsd:annotation>
					<xsd:documentation>The Reporting Platform Operator does not have a residence for tax purposes but it is incorporated under the laws of the EU Member State.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RPONEX3">
				<xsd:annotation>
					<xsd:documentation>The Reporting Platform Operator does not have a residence for tax purposes but it has its place of management (including effective management) in the EU Member State.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RPONEX4">
				<xsd:annotation>
					<xsd:documentation>The Reporting Platform Operator does not have a residence for tax purposes but it has a permanent establishment in the EU Member State and it is not a Qualified Non-Union Platform Operator.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="RPONEX5">
				<xsd:annotation>
					<xsd:documentation>The Reporting Platform Operator is neither resident for tax purposes, nor incorporated or managed in the EU Member State, nor has a permanent establishment in the EU Member State but it facilitates the carrying out of a Relevant Activity by Reportable Sellers or a Relevant Activity involving the rental of immovable property located in the EU Member State and it is not a Qualified Non-Union Platform Operator.</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!--  -->
	<!--  -->
	<!--++++++++++++++++++ Reusable Complex types +++++++++++++++++++++++++++++++++++++ -->
	<!-- -->
	<!--The Name of a Party, given in fixed Form-->
	<xsd:complexType name="NamePerson_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">The user must spread the data about the name of a party over up to six elements. The container element for this will be 'NameFix'. </xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="PrecedingTitle" type="stf:StringMin1Max200_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">His Excellency,Estate of the Late ...</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Title" type="stf:StringMin1Max200_Type" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">Greeting title. Example: Mr, Dr, Ms, Herr, etc. Can have multiple titles.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="FirstName">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">FirstName of the person</xsd:documentation>
				</xsd:annotation>
				<xsd:complexType>
					<xsd:simpleContent>
						<xsd:extension base="stf:StringMin1Max200_Type">
							<xsd:attribute name="xnlNameType" type="stf:StringMin1Max200_Type">
								<xsd:annotation>
									<xsd:documentation xml:lang="en">Defines the name type of FirstName. Example: Given Name, Forename, Christian Name, Father's Name, etc. In some countries, FirstName could be a Family Name or a SurName. Use this attribute to define the type for this name.
									</xsd:documentation>
								</xsd:annotation>
							</xsd:attribute>
						</xsd:extension>
					</xsd:simpleContent>
				</xsd:complexType>
			</xsd:element>
			<xsd:element name="MiddleName" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">Middle name (essential part of the name for many nationalities). Example: Sakthi in "Nivetha Sakthi Shantha". Can have multiple middle names.</xsd:documentation>
				</xsd:annotation>
				<xsd:complexType>
					<xsd:simpleContent>
						<xsd:extension base="stf:StringMin1Max200_Type">
							<xsd:attribute name="xnlNameType" type="stf:StringMin1Max200_Type">
								<xsd:annotation>
									<xsd:documentation xml:lang="en">Defines the name type of Middle Name. Example: First name, middle name, maiden name, father's name, given name, etc.
									</xsd:documentation>
								</xsd:annotation>
							</xsd:attribute>
						</xsd:extension>
					</xsd:simpleContent>
				</xsd:complexType>
			</xsd:element>
			<xsd:element name="NamePrefix" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">de, van, van de, von, etc. Example: Derick de Clarke</xsd:documentation>
				</xsd:annotation>
				<xsd:complexType>
					<xsd:simpleContent>
						<xsd:extension base="stf:StringMin1Max200_Type">
							<xsd:attribute name="xnlNameType" type="stf:StringMin1Max200_Type">
								<xsd:annotation>
									<xsd:documentation xml:lang="en">Defines the type of name associated with the NamePrefix. For example the type of name is LastName and this prefix is the prefix for this last name.
							</xsd:documentation>
								</xsd:annotation>
							</xsd:attribute>
						</xsd:extension>
					</xsd:simpleContent>
				</xsd:complexType>
			</xsd:element>
			<xsd:element name="LastName">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">Represents the position of the name in a name string. Can be Given Name, Forename, Christian Name, Surname, Family Name, etc. Use the attribute "NameType" to define what type this name is.
In case of a company, this field can be used for the company name.</xsd:documentation>
				</xsd:annotation>
				<xsd:complexType>
					<xsd:simpleContent>
						<xsd:extension base="stf:StringMin1Max200_Type">
							<xsd:attribute name="xnlNameType" type="stf:StringMin1Max200_Type">
								<xsd:annotation>
									<xsd:documentation xml:lang="en">Defines the name type of LastName. Example: Father's name, Family name, Sur Name, Mother's Name, etc. In some countries, LastName could be the given name or first name.
									</xsd:documentation>
								</xsd:annotation>
							</xsd:attribute>
						</xsd:extension>
					</xsd:simpleContent>
				</xsd:complexType>
			</xsd:element>
			<xsd:element name="GenerationIdentifier" type="stf:StringMin1Max200_Type" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">Jnr, Thr Third, III</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Suffix" type="stf:StringMin1Max200_Type" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">Could be compressed initials - PhD, VC, QC</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="GeneralSuffix" type="stf:StringMin1Max200_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">Deceased, Retired ...</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
		<xsd:attribute name="nameType" type="stf:OECDNameType_EnumType" use="optional">
			<xsd:annotation>
				<xsd:documentation xml:lang="en">It is possible for STF documents to contain several names for the same party. This attribute is a qualifier to indicate the type of a particular name.</xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
	</xsd:complexType>
	<!-- -->
	<!--Collection of all Data describing a person as a  Party -->
	<xsd:complexType name="PersonParty_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">This container brings together all data about a person as a party. Name and address are required components and each can be present more than once to enable as complete a description as possible. Whenever possible one or more identifiers (TIN etc) should be added as well as a residence country code. Additional data that describes and identifies the party can be given. The code for the legal type according to the OECD codelist must be added. The structures of all of the subelements are defined elsewhere in this schema.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="ResCountryCode" type="iso:CountryCode_Type" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>This repeatable data element describes the residence country code(s) of the individual Seller and must be present in all data records. This should correspond to the jurisdiction of residence identified on the basis of the due diligence requirements of the OECD Model Rules or [EU Specific] [EU DIR2021/514]. Specifically, under the OECD Model Rules, the residence country code of an individual Seller should correspond to the jurisdiction of the Seller’s primary residence.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="TIN" type="dpi:TIN_Type" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>This repeatable data element provides the tax identification number (TIN) used by the tax administration of the jurisdiction of residence of the individual Seller. In case the individual Seller does not have a TIN, the jurisdiction of residence does not issue a TIN or require the collection of the TIN issued to such Seller, or the TIN is not known to the sending Competent Authority, the Unknown attribute (see below) must be set to “true” and the value “NOTIN” should be entered [OECD Specific]. Furthermore, in case more than one TIN are provided, any provided element cannot be flagged as “unknown”.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="VAT" type="stf:StringMin1Max200_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">This data element must be provided when a VAT Identification number is available.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Name" type="dpi:NamePerson_Type" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>This element should contain the name of the person.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Address" type="dpi:Address_Type" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>This data element should contain the address of the person, including the country code of the address as well as the type of the address, indicating the legal character of that address.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Nationality" type="iso:CountryCode_Type" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>The nationality element is not to be provided as part of the DPI schema.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="BirthInfo">
				<xsd:annotation>
					<xsd:documentation>This data element contains the birth information of an Individual Seller. It is always required to be provided unless such Seller is documented pursuant to a Government Verification Service and is composed of the date and the place of birth.</xsd:documentation>
				</xsd:annotation>
				<xsd:complexType>
					<xsd:sequence>
						<xsd:element name="BirthDate" type="xsd:date">
							<xsd:annotation>
								<xsd:documentation>This element provides the date of birth, complying with the following format: YYYY-MM-DD.</xsd:documentation>
							</xsd:annotation>
						</xsd:element>
						<xsd:element name="BirthPlace" type="dpi:BirthPlace_Type" minOccurs="0">
							<xsd:annotation>
								<xsd:documentation>This element provides information about the place of birth. This element must be filled in at least with the city and the country of birth (either the current jurisdiction identified by 2-characters country code or a former jurisdiction identified by a name).</xsd:documentation>
							</xsd:annotation>
						</xsd:element>
					</xsd:sequence>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- -->
	<!--Address Fix -->
	<xsd:complexType name="AddressFix_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">Structure of the address for a party broken down into its logical parts, recommended for easy matching. The 'City' element is the only required subelement. All of the subelements are simple text - data type 'string'.
			</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="Street" type="stf:StringMin1Max200_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The street.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="BuildingIdentifier" type="stf:StringMin1Max200_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The identifier of the building on the street, typically a number.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="SuiteIdentifier" type="stf:StringMin1Max200_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The identifier of an office or similar part of a building.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="FloorIdentifier" type="stf:StringMin1Max200_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The identifier of a floor within a building.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="DistrictName" type="stf:StringMin1Max200_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The name of the district of the address.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="POB" type="stf:StringMin1Max200_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The post office box.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="PostCode" type="stf:StringMin1Max200_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The post code of the address, which must be provided if available.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="City" type="stf:StringMin1Max200_Type">
				<xsd:annotation>
					<xsd:documentation>The city of the address.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CountrySubentity" type="stf:StringMin1Max200_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>A geographic area of the country larger than district or city, for example a county, a department, a Land, a canton, etc.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!--  -->
	<!--The Address of a Party, given in fixed or free Form, possibly in both Forms -->
	<xsd:complexType name="Address_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">The user has the option to enter the data about the address of a party either as one long field or to spread the data over up to eight  elements or even to use both formats. If the user chooses the option to enter the data required in separate elements, the container element for this will be 'AddressFix'. If the user chooses the option to enter the data required in a less structured way in 'AddressFree' all available address details shall be presented as one string of bytes, blank or "/" (slash) or carriage return- line feed used as a delimiter between parts of the address. PLEASE NOTE that the address country code is outside  both of these elements. The use of the fixed form is recommended as a rule to allow easy matching. However, the use of the free form is recommended if the sending state cannot reliably identify and distinguish the different parts of the address. The user may want to use both formats e.g. if besides separating the logical parts of the address he also wants to indicate a suitable breakdown into print-lines by delimiters in the free text form. In this case 'AddressFix' has to precede 'AddressFree'.
			</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="CountryCode">
				<xsd:annotation>
					<xsd:documentation>This data element provides the country code associated with the entity’s (or person’s) address.</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="iso:CountryCode_Type">
						<xsd:minLength value="1"/>
						<xsd:maxLength value="2"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
			<xsd:choice>
				<xsd:element name="AddressFree" type="stf:StringMin1Max4000_Type">
					<xsd:annotation>
						<xsd:documentation>This data element allows input of address information in free text. It should only be used in exceptional circumstances when it is impossible to provide the address in the fixed format.</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:sequence>
					<xsd:element name="AddressFix" type="dpi:AddressFix_Type"/>
					<xsd:element name="AddressFree" type="stf:StringMin1Max4000_Type" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>This data element allows input of address information in free text. It should only be used in exceptional circumstances when it is impossible to provide the address in the fixed format.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:choice>
		</xsd:sequence>
		<xsd:attribute name="legalAddressType" type="stf:OECDLegalAddressType_EnumType" use="optional">
			<xsd:annotation>
				<xsd:documentation>This is a datatype for an attribute to an address. It serves to indicate the legal character of that address (residential, business etc.).
				The possible values are:
				‒	OECD301= residentialOrBusiness
				‒	OECD302= residential
				‒	OECD303= business
				‒	OECD304= registeredOffice
				‒	OECD305= unspecified
				The address of the Reportable Platform Operator must represent the “Registered Office Address” (OECD304).</xsd:documentation>
			</xsd:annotation>
		</xsd:attribute>
	</xsd:complexType>
	<!--  -->
	<!--The place of birth -->
	<xsd:complexType name="BirthPlace_Type">
		<xsd:annotation>
			<xsd:documentation>This element provides information about the place of birth. This element must be filled in at least with the city and the country of birth (either the current jurisdiction identified by 2-characters country code or a former jurisdiction identified by a name).</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="City" type="stf:StringMin1Max200_Type">
				<xsd:annotation>
					<xsd:documentation>The city of birth.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CitySubentity" type="stf:StringMin1Max200_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The city subentity of birth.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CountryInfo">
				<xsd:complexType>
					<xsd:choice>
						<xsd:element name="CountryCode" type="iso:CountryCode_Type">
							<xsd:annotation>
								<xsd:documentation>The current jurisdiction of birth.</xsd:documentation>
							</xsd:annotation>
						</xsd:element>
						<xsd:element name="FormerCountryName" type="stf:StringMin1Max200_Type">
							<xsd:annotation>
								<xsd:documentation>The former jurisdiction of birth. The Former Country Name element should be used in case the person was born in a country that has since ceased to exist.</xsd:documentation>
							</xsd:annotation>
						</xsd:element>
					</xsd:choice>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!--  -->
	<!--General Type for Monetary Amounts -->
	<xsd:complexType name="MonAmnt_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">This data type is to be used whenever monetary amounts are to be communicated. Such amounts shall be given in full units, i.e. without decimals.  The code for the currency in which the value is expressed has to be taken from the ISO codelist 4217 and added in attribute currCode.
</xsd:documentation>
		</xsd:annotation>
		<xsd:simpleContent>
			<xsd:extension base="xsd:integer">
				<xsd:attribute name="currCode" type="iso:currCode_Type" use="required"/>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<!--  -->
	<!--Organisation name -->
	<xsd:complexType name="NameOrganisation_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">Name of organisation</xsd:documentation>
		</xsd:annotation>
		<xsd:simpleContent>
			<xsd:extension base="stf:StringMin1Max200_Type"/>
		</xsd:simpleContent>
	</xsd:complexType>
	<!-- -->
	<!--Organisation name (Reportable Seller)-->
	<xsd:complexType name="NameReportableSeller_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">Name of Seller</xsd:documentation>
		</xsd:annotation>
		<xsd:simpleContent>
			<xsd:extension base="stf:StringMin1Max200_Type"/>
		</xsd:simpleContent>
	</xsd:complexType>
	<!-- -->
	<!--TIN -->
	<xsd:complexType name="TIN_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">This is the identification number/identification code for the party in question. As the identifier may be not strictly numeric, it is just defined as a string of characters. Attribute 'issuedBy' is required to designate the issuer of the identifier. </xsd:documentation>
		</xsd:annotation>
		<xsd:simpleContent>
			<xsd:extension base="stf:StringMin0Max200_Type">
				<xsd:attribute name="issuedBy" type="iso:CountryCode_Type" use="optional">
					<xsd:annotation>
						<xsd:documentation xml:lang="en">Country code of issuing country, indicating country of Residence (to taxes and other).
						It should always be provided, unless the TIN element is flagged as “unknown”.</xsd:documentation>
					</xsd:annotation>
				</xsd:attribute>
				<xsd:attribute name="unknown" type="xsd:boolean" use="optional">
					<xsd:annotation>
						<xsd:documentation xml:lang="en">This attribute must be provided if the TIN is not available or inexistent. Any value provided for a TIN flagged as unknown will be discarded.</xsd:documentation>
					</xsd:annotation>
				</xsd:attribute>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<!-- -->
	<!--Message specification: Data identifying and describing the message as a whole-->
	<xsd:complexType name="MessageSpec_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">Information in the message header identifies the Tax Administration that is sending the message.  It specifies when the message was created, what period (normally a year) the report is for, and the nature of the report (original, corrected, supplemental, etc).</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="SendingEntityIN" type="stf:StringMin1Max200_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Although not used for exchanges between Competent Authorities under the DPI MCAA and [EU DIR2021/514], for domestic reporting, this data element can be used in case the schema is mandated for domestic reporting by Reporting Platform Operators to their tax administration. In such instances, it identifies the Reporting Platform Operator sending the message through a domestically-defined identification number.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="TransmittingCountry" type="iso:CountryCode_Type">
				<xsd:annotation>
					<xsd:documentation>This data element identifies the jurisdiction of the Competent Authority transmitting the DPI message.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ReceivingCountry" type="iso:CountryCode_Type">
				<xsd:annotation>
					<xsd:documentation>This data element identifies the jurisdiction of the Competent Authority receiving the DPI message.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="MessageType" type="dpi:MessageType_EnumType">
				<xsd:annotation>
					<xsd:documentation>This data element specifies the type of message being sent. The only allowable entry for messages exchanged under the OECD Model Rules and [EU Specific] [EU DIR2021/514] in this field is “DPI”.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Warning" type="stf:StringMin1Max4000_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">Free text expressing the restrictions for use of the information this message contains and the legal framework under which it is given.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Contact" type="stf:StringMin1Max4000_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">All necessary contact information about persons responsible for and involved in the processing of the data transmitted in this message, both legally and technically. Free text as this is not intended for automatic processing.
					In international exchanges, this data element contains the contact details of the sending competent authority.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="MessageRefId" type="stf:StringMin1Max170_Type">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">Sender's unique identifier for this message.
					The Message RefID must start with the country code of the sending jurisdiction, then the year of the reportable period, then the receiving country code before a unique identifier.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="MessageTypeIndic" type="dpi:DPIMessageTypeIndic_EnumType">
				<xsd:annotation>
					<xsd:documentation>This data element specifies the type of information that is sent, i.e. whether it is new information or whether the message seeks to correct or delete previously sent information.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ReportingPeriod" type="xsd:date">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">The reporting year for which information is transmitted in documents of the current message. This is in yyyy-MM-DD format, on the basis of the calendar year in which the relevant Reportable Period under the OECD Model Rules or [EU Specific] [EU DIR2021/514] ended.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Timestamp" type="xsd:dateTime">
				<xsd:annotation>
					<xsd:documentation>This data element identifies the date and time when the message was compiled. It is anticipated that this element will be automatically populated by the host system. The format for use is yyyy-MM-DD’T’hh:mm:ss.nnn. Fractions of seconds may be used (in such a case the milli-seconds will be provided on 3 digits, see “.nnn” in the format above). </xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- -->
	<!--Organisation Identification Number -->
	<xsd:complexType name="OrganisationIN_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">This is the identification number/identification code for the Entity in question. As the identifier may be not strictly numeric, it is just defined as a string of characters. Attribute 'issuedBy' is required to designate the issuer of the identifier.  Attribute 'INType' defines the type of identification number. </xsd:documentation>
		</xsd:annotation>
		<xsd:simpleContent>
			<xsd:extension base="stf:StringMin1Max200_Type">
				<xsd:attribute name="issuedBy" type="iso:CountryCode_Type" use="optional">
					<xsd:annotation>
						<xsd:documentation xml:lang="en">Country code of issuing country, indicating country of Residence (to taxes and other)</xsd:documentation>
					</xsd:annotation>
				</xsd:attribute>
				<xsd:attribute name="INType" type="dpi:INType_EnumType" use="required">
					<xsd:annotation>
						<xsd:documentation xml:lang="en">This attribute defines the type of identification number being sent among the following:
						•	[EU Specific]: IIN for the reporting of an individual identification number;
						•	LEI for the reporting of a legal entity identifier;
						•	EIN for the reporting of an entity identification number; 
						•	BRN for the reporting of a business registration number; or
						•	Other.
</xsd:documentation>
					</xsd:annotation>
				</xsd:attribute>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<!-- -->
	<!--Collection of all Data describing an organisationy  as party-->
	<xsd:complexType name="OrganisationParty_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">
			This container brings together all data about an organisation as a party. Name and address are required components and each can be present more than once to enable as complete a description as possible. Whenever possible one or more identifiers (TIN etc) should be added as well as a residence country code. Additional data that describes and identifies the party can bgiven . The code for the legal type according to the OECD codelist must be added. The structures of all of the subelements are defined elsewhere in this schema.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="ResCountryCode" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>This repeatable data element describes the residence country code(s) of the entity. 
In case of an Entity Seller this should always be present and should correspond to the jurisdiction of residence identified on the basis of the due diligence requirements of the OECD Model Rules or [EU Specific] [EU DIR2021/514]. Specifically, under the OECD Model Rules, the residence country code of an Entity Seller should correspond to the jurisdiction in which its registered office is located.
In case of a Reporting Platform Operator, the residence country code should correspond to the jurisdiction where the Reporting Platform Operator is resident for tax purposes or, where it does not have a residence for tax purposes, either the jurisdiction it is incorporated under or the jurisdiction that it has its place of management (including effective management) in, [EU Specific] or the Member State where it has a permanent establishment in.
[EU Specific]: Reporting Platform Operator: This element is optional.</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="iso:CountryCode_Type"/>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="TIN" type="dpi:TIN_Type" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>This repeatable data element provides the tax identification number (TIN) used by the tax administration of the jurisdiction of residence of the entity. In case the entity does not have a TIN, or the TIN is not known  to the sending Competent Authority, the value “NOTIN” should be entered [OECD Specific] and the Unknown attribute (see below) must be set to “true”. Furthermore, in case more than one TIN are provided, any provided element cannot be flagged as “unknown”.
					[EU Specific]: This element must be present for both Reporting Platform Operator and Entity Seller.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="IN" type="dpi:OrganisationIN_Type" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">This data element can be provided (and repeated) if there are other INs available, such as a company registration number or an Entity Identification Number (EIN).
					[EU Specific]:
					•	Reporting Platform Operator: Where relevant, the individual identification number (IIN) shall be provided
					•	Entity Seller: The business registration number (BRN) must be provided</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="VAT" type="stf:StringMin1Max200_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">This data element can be provided when a VAT Identification number is available.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Name" type="dpi:NameOrganisation_Type" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>This element should contain the legal name of the entity, including the domestic designation for the legal form, as indicated in its articles of incorporation or any similar document.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="PlatformBusinessName" type="stf:StringMin1Max200_Type" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>The repeatable Platform Business Name element identifies the business name(s) of theother Reporting Platform(s) in respect of which the Reporting Platform Operator is reporting. 
This element must not be used for the Entity Sellers.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Address" type="dpi:Address_Type" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>This data element should contain the address of the entity, including the country code of the address as well as the type of the address, indicating the legal character of that address.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Nexus" type="dpi:Nexus_EnumType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>[EU Specific] This data element provides the reason for which the information will be reported to the competent authority of the EU Member State.
					This data element must not be used for Entity Seller.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="AssumedReporting" type="xsd:boolean" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The Assumed Reporting element identifies whether the Reporting Platform Operator is not reporting information on Reportable Sellers, because another Reporting Platform Operator has assumed the reporting.
					When “AssumedReporting” is set to “true”, it means that another Reporting Platform Operator reported for the Reporting Platform Operator. Otherwise, this element must be set to “false”.
					This element is mandatory for Reporting Platform Operator and must not be provided for an Entity Seller.
					By way of an example, Platform Z is operated by two Platform Operators: Platform Operator 1 (resident in jurisdiction 1) and Platform Operator 2 (resident in jurisdiction 2). In satisfying the reporting requirements, Platform Operator 1 has obtained assurances from Platform Operator 2 that it will fulfil the reporting obligations with respect to all of the Reportable Sellers of Platform Operator 1 in jurisdiction 2. 
					Platform Operator 1 will therefore provide its identification information and set the AssumedReporting element to “true” to notify its jurisdiction residence (jurisdiction 1) that it has relied on another Platform Operator to fulfil the reporting requirements. Platform Operator 1 will also use the AssumingPlatformOperator element (discussed further below) to report to its jurisdiction (jurisdiction 1) identification information on Platform Operator 2, assuming the reporting obligation in the name of Platform Operator 1. Platform Operator 1 will not provide ReportableSeller element.
					At the same time, Platform Operator 2 will use the AssumedPlatformOperator element (discussed further below) to report to its jurisdiction of residence (jurisdiction 2) identification information on Platform Operator 1, for which it has assumed reporting obligations, and complete the Platform Operator element for itself.
					[EU Specific] This element is optional for the purposes of [EU DIR2021/514].
</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- -->
	<!--Correctable Platform Operator_Type-->
	<xsd:complexType name="CorrectablePlatformOperator_Type">
		<xsd:annotation>
			<xsd:documentation>This correctable extends the information about the Reporting Platform Operator by considering the DocSpec element used to identify the particular report within the DPI message being transmitted.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:extension base="dpi:OrganisationParty_Type">
				<xsd:sequence>
					<xsd:element name="DocSpec" type="stf:DocSpec_Type">
						<xsd:annotation>
							<xsd:documentation>DocSpec identifies the particular report within the DPI message being transmitted. It permits the identification of reports requiring correction.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- -->
	<!--Correctable Reportable Seller-->
	<xsd:complexType name="CorrectableReportableSeller_Type">
		<xsd:annotation>
			<xsd:documentation>This correctable extends the information about the Reportable Seller by considering the DocSpec element used to identify the particular report within the DPI message being transmitted.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:extension base="dpi:ReportableSeller_Type">
				<xsd:sequence>
					<xsd:element name="DocSpec" type="stf:DocSpec_Type">
						<xsd:annotation>
							<xsd:documentation>DocSpec identifies the particular report within the DPI message being transmitted. It permits the identification of reports requiring correction.</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- -->
	<!--Additional Info -->
	<xsd:complexType name="CorrectableAdditionalInfo_Type">
		<xsd:sequence>
			<xsd:element name="DocSpec" type="stf:DocSpec_Type"/>
			<xsd:element name="OtherInfo" type="stf:StringMin1Max4000WithLang_Type" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>Please include any further brief information or explanation you consider necessary or that would facilitate the understanding of the compulsory information provided in the country-by-country report. </xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ResCountryCode" type="iso:CountryCode_Type" minOccurs="0" maxOccurs="unbounded"/>
			<!-- <xsd:element name="SummaryRef" type="dpi:DPISummaryListElementsType_EnumType" minOccurs="0" maxOccurs="unbounded"/> -->
		</xsd:sequence>
	</xsd:complexType>
	<!--  -->
	<!--Duplicate of Account Holder Type (from CRS)-->
	<xsd:complexType name="AccountHolder_Type">
		<xsd:sequence>
			<xsd:choice>
				<xsd:element name="Individual" type="dpi:NameReportableSeller_Type"/>
				<xsd:sequence>
					<xsd:element name="Organisation" type="dpi:OrganisationParty_Type"/>
					<xsd:element name="AcctHolderType"/>
				</xsd:sequence>
			</xsd:choice>
		</xsd:sequence>
	</xsd:complexType>
	<!--  -->
	<!--Financial Identifer for Identity-->
	<xsd:complexType name="FinancialIdentifier_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">The Financial Identifier is an optional (mandatory) element that reflects the Financial Account Identifier which, under the OECD Model Rules or [EU Specific] [EU DIR2021/514], is the unique identifying number or reference available to the Reporting Platform Operator of the bank account or other payment account to which the Consideration is paid or credited. Under subparagraphs B(2)(c) and B(3)(c) of Section III of the OECD Model Rules or [EU Specific] subparagraphs B(2)(b) and B(3)(b) of Section III of [EU DIR2021/514], the Financial Identifier must be reported and exchanged provided that it is available to the Reporting Platform Operator and that the jurisdiction of the Reportable Seller’s residence has indicated that it wishes to receive such Financial Identifiers for taxpayer matching purposes.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="Identifier" type="dpi:Identifier_Type">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">Entity Identification Number, which contains the Financial Account Identifier pertaining to the Reportable Seller should be reflected. Financial Account Identifiers can include the IBAN number, sort code and account number and any other payment account identifier that the Reporting Platform Operator used for transferring the Consideration in respect to a Reportable Seller.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="AccountHolderName" type="stf:StringMin1Max200_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The Account Holder Name field is an optional (mandatory) element which, reflecting subparagraphs B(2)(d) and B(3)(d) of Section III of the OECD Model Rules or [EU Specific] subparagraphs B(2)(c) and B(3)(c) of Section III of [EU DIR2021/514], contains the name of the holder of the financial account to which the Consideration is paid or credited, where different from the name of the Reportable Seller and to the extent available to the Reporting Platform Operator.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="OtherInfo" type="stf:StringMin1Max400_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Reflecting subparagraphs B(2)(d) and B(3)(d) of Section III of the OECD Model Rules or [EU Specific] subparagraphs B(2)(c) and B(3)(c) of Section III of [EU DIR2021/514], the OtherInfo field contains any other identifying information available to the Reporting Platform Operator with respect to that account holder.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!--  -->
	<!--Identifer for Financial Identifier-->
	<xsd:complexType name="Identifier_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">The Identifier field is a required element which contains the Financial Account Identifier pertaining to the Reportable Seller should be reflected. Financial Account Identifiers can include the IBAN number, sort code and account number and any other payment account identifier that the Reporting Platform Operator used for transferring the Consideration in respect to a Reportable Seller.</xsd:documentation>
		</xsd:annotation>
		<xsd:simpleContent>
			<xsd:extension base="stf:StringMin1Max200_Type">
				<xsd:attribute name="AccountNumberType" type="stf:StringMin1Max200_Type">
					<xsd:annotation>
						<xsd:documentation xml:lang="en">This attribute describes the type of account number being sent.</xsd:documentation>
					</xsd:annotation>
				</xsd:attribute>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<!--  -->
	<!--OtherActivities (Complex)-->
	<xsd:complexType name="OtherActivities_Type">
		<xsd:sequence>
			<xsd:element name="Consideration" type="dpi:ConsiderationType">
				<xsd:annotation>
					<xsd:documentation>The Consideration element contains information on the Consideration received by a Reportable Seller in relation to the Relevant Activities provided. It is further split into four elements, representing the quarters during which the Consideration was paid or credited to a Reportable Seller. In this respect, Consideration is considered to be paid or credited to a Reportable Seller when it is paid or credited to an account specified by the Reportable Seller (as stated in paragraph 32 of the Commentary to Section I of the OECD  Model Rules).</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="NumberOfActivities" type="dpi:NumberOfActivities_Type">
				<xsd:annotation>
					<xsd:documentation>The Number of Activities element specified the number of Relevant Activities that a Reportable Seller has provided. It is further split into four elements. These elements represent the four quarters in respect of which reporting of the number of Relevant Activities in respect of which Consideration was paid or credited to the Reportable Seller is required. As such, that the numbers of activities are reported on the basis of the date of payment or credit of the Consideration.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Fees" type="dpi:FeesType">
				<xsd:annotation>
					<xsd:documentation>The Fees element is further split into four elements, representing the quarters in respect of which reporting takes place. </xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Taxes" type="dpi:TaxesType">
				<xsd:annotation>
					<xsd:documentation>The Taxes element is further split into four elements, representing the quarters in respect of which reporting takes place. </xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!--  -->
	<!--Number of Activities (Complex)-->
	<xsd:complexType name="NumberOfActivities_Type">
		<xsd:annotation>
			<xsd:documentation>The Number of Services element is further split into four elements, representing the quarters in respect of which reporting takes place. </xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="NumbQ1" type="xsd:integer"/>
			<xsd:element name="NumbQ2" type="xsd:integer"/>
			<xsd:element name="NumbQ3" type="xsd:integer"/>
			<xsd:element name="NumbQ4" type="xsd:integer"/>
		</xsd:sequence>
	</xsd:complexType>
	<!--  -->
	<!--Taxes Type (Complex)-->
	<xsd:complexType name="TaxesType">
		<xsd:annotation>
			<xsd:documentation>The Taxes element is further split into four elements, representing the quarters in respect of which reporting takes place.
			Each quarter element is further comprised of the MonAmnt_Type, used to communicate taxes withheld in respect of Sellers. Such amounts shall be given in full units, i.e. without decimals. The code for the currency, in which the value is expressed has to be taken from the ISO code list 4217 and added in attribute currCode.</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="TaxQ1" type="dpi:MonAmnt_Type"/>
			<xsd:element name="TaxQ2" type="dpi:MonAmnt_Type"/>
			<xsd:element name="TaxQ3" type="dpi:MonAmnt_Type"/>
			<xsd:element name="TaxQ4" type="dpi:MonAmnt_Type"/>
		</xsd:sequence>
	</xsd:complexType>
	<!--  -->
	<!--Fees Type (Complex) -->
	<xsd:complexType name="FeesType">
		<xsd:sequence>
			<xsd:annotation>
				<xsd:documentation>The Fees element is further split into four elements, representing the quarters in respect of which reporting takes place.
				Each quarter element is further comprised of the MonAmnt_Type, used to communicate the fees withheld in respect of Sellers. Such amounts shall be given in full units, i.e. without decimals. The code for the currency, in which the value is expressed has to be taken from the ISO code list 4217 and added in attribute currCode.</xsd:documentation>
			</xsd:annotation>
			<xsd:element name="FeesQ1" type="dpi:MonAmnt_Type"/>
			<xsd:element name="FeesQ2" type="dpi:MonAmnt_Type"/>
			<xsd:element name="FeesQ3" type="dpi:MonAmnt_Type"/>
			<xsd:element name="FeesQ4" type="dpi:MonAmnt_Type"/>
		</xsd:sequence>
	</xsd:complexType>
	<!--  -->
	<!--Consideration Type (Complex) -->
	<xsd:complexType name="ConsiderationType">
		<xsd:sequence>
			<xsd:annotation>
				<xsd:documentation>The Consideration element is further split into four elements, representing the quarters in respect of which reporting takes place.
				Each quarter element is further comprised of the MonAmnt_Type, used to communicate the monetary amounts earned by Sellers. Such amounts shall be given in full units, i.e. without decimals. The code for the currency, in which the value is expressed has to be taken from the ISO code list 4217 and added in attribute currCode.</xsd:documentation>
			</xsd:annotation>
			<xsd:element name="ConsQ1" type="dpi:MonAmnt_Type"/>
			<xsd:element name="ConsQ2" type="dpi:MonAmnt_Type"/>
			<xsd:element name="ConsQ3" type="dpi:MonAmnt_Type"/>
			<xsd:element name="ConsQ4" type="dpi:MonAmnt_Type"/>
		</xsd:sequence>
	</xsd:complexType>
	<!--  -->
	<!--Property Listing Type (Complex) -->
	<xsd:complexType name="PropertyListingType">
		<xsd:sequence>
			<xsd:element name="Address" type="dpi:Address_Type">
				<xsd:annotation>
					<xsd:documentation>The Address element is further comprised of the elements as described above under the Address Type.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="LandRegistrationNumber" type="stf:StringMin1Max200_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The Land Registration Number element contains the land registration number, which under subparagraph B(3)(f) of Section III of the OECD Model Rules or [EU Specific] subparagraph B(3)(e) of Section III of [EU DIR2021/514], is reportable if available to the Reporting Platform Operator. For these purposes, the land registration number includes functional equivalents, such as a cadastral number.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Consideration" type="dpi:ConsiderationType">
				<xsd:annotation>
					<xsd:documentation>The Consideration element contains information on the Consideration received by a Reportable Seller in relation to the Relevant Activities provided. It is further split into four elements, representing the quarters during which the Consideration was paid or credited to a Reportable Seller. In this respect, Consideration is considered to be paid or credited to a Reportable Seller when it is paid or credited to an account specified by the Reportable Seller (as stated in paragraph 32 of the Commentary to Section I of the OECD  Model Rules).
					For Relevant Activities involving the rental of immovable property, the Consideration information must be provided separately with respect to each Property Listing.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="NumberOfActivities" type="dpi:NumberOfActivities_Type">
				<xsd:annotation>
					<xsd:documentation>The Number of Activities element specified the number of Relevant Activities that a Reportable Seller has provided. It is further split into four elements. These elements represent the four quarters in respect of which reporting of the number of Relevant Activities in respect of which Consideration was paid or credited to the Reportable Seller is required.
					For Relevant Activities involving the rental of immovable property, the number of activities must be provided separately with respect to each Property Listing.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Fees" type="dpi:FeesType">
				<xsd:annotation>
					<xsd:documentation>The Fees element specified the fees and commissions that a Reporting Platform Operator has withheld in respect of a Reportable Seller. It is split into four elements, representing the quarters in respect of which the reporting of fees or commissions withheld or charged by the Reporting Platform Operator is required under subparagraphs B(2)(g) and B(3)(h) of Section III of the OECD Model Rules or [EU Specific] subparagraphs B(2)(f) and B(3)(g) of Section III of [EU DIR2021/514]. </xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Taxes" type="dpi:TaxesType">
				<xsd:annotation>
					<xsd:documentation>The Taxes element specifies the taxes that a Reporting Platform Operator has withheld in respect of a Reportable Seller. It is further split into four elements, representing the quarters in respect of which reporting of taxes withheld by the Reporting Platform Operator is required under subparagraphs B(2)(g) and B(3)(h) of Section III of the OECD Model Rules or [EU Specific] subparagraphs B(2)(f) and B(3)(g) of Section III of [EU DIR2021/514].</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="PropertyType" type="dpi:DPIPropertyType_EnumType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The Property Type element specifies the type of property rented. DPI901 to DPI910.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="OtherPropertyType" type="stf:StringMin1Max200_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>This element describes the type of property in case “DPI910” is selected as Property Type. This element cannot be used in case another Property Type is selected.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="RentedDays" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The Rented Days element contains the number of days that the Property Listing was rented during the Reportable Period.
					4 numbers max. Set the type as simple</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="xsd:integer">
						<xsd:totalDigits value="4"/>
						<xsd:minInclusive value="1"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!--  -->
	<!--GVS Type (Complex) -->
	<xsd:complexType name="GVSType">
		<xsd:annotation>
			<xsd:documentation>The GVS element reflects the Government Verification Service (GVS) due diligence procedures and is composed of the Name GVS, Jurisdiction GVS, Reference GVS and Other TIN GVS elements, which contain the information items subject to reporting (and exchange) in respect of a Reportable Seller that has been identified on the basis of a Government Verification Service, as well as the Financial Identifier element.
			[EU Specific] At the time of publication, the collection and exchange of GVS information is not a legal requirement under [EU DIR2021/514].</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="NameGVS" type="stf:StringMin1Max200_Type">
				<xsd:annotation>
					<xsd:documentation>The Name GVS element contains the legal name of the Reportable Seller.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="JurisdictionGVS">
				<xsd:annotation>
					<xsd:documentation>The Jurisdiction GVS element identifies the jurisdiction whose Government Verification Service was relied upon by the Reporting Platform Operator in respect of the Reportable Seller.
					It uses the 2-character alphabetic country code and country name list based on the ISO 3166-1 Alpha 2 standard.</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="iso:CountryCode_Type"/>
				</xsd:simpleType>
			</xsd:element>
			<xsd:element name="ReferenceGVS" type="stf:StringMin1Max200_Type">
				<xsd:annotation>
					<xsd:documentation>The Reference GVS element contains the unique reference number or code provided by the jurisdiction whose GVS was used, allowing that jurisdiction to match the Reportable Seller to a taxpayer within its database.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="OtherTINGVS" type="stf:StringMin1Max200_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Reflecting subparagraph B(2)(b) and B(3)(b) of Section III of the OECD Model Rules, the Other TIN GVS element contains any TIN available to the Reporting Platform Operator, including the jurisdiction of issuance.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="FinancialIdentifier" type="dpi:FinancialIdentifier_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The Financial Identifier is an optional (mandatory) element that reflects the Financial Account Identifier which is the unique identifying number or reference available to the Reporting Platform Operator of the bank account or other payment account to which the Consideration is paid or credited.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!--  -->
	<!-- Reportable Seller (Complex)-->
	<xsd:complexType name="ReportableSeller_Type">
		<xsd:sequence>
			<xsd:element name="Identity">
				<xsd:annotation>
					<xsd:documentation>The Identity element is further comprised of the EntitySeller and IndividualSeller elements.</xsd:documentation>
				</xsd:annotation>
				<xsd:complexType>
					<xsd:choice>
						<xsd:element name="EntitySeller">
							<xsd:annotation>
								<xsd:documentation>The Entity Seller element is further comprised of the Standard and GVS elements.</xsd:documentation>
							</xsd:annotation>
							<xsd:complexType>
								<xsd:choice>
									<xsd:element name="Standard">
										<xsd:annotation>
											<xsd:documentation>The Standard element reflects the standard due diligence procedures and is further comprised of the EntSellerID (that follows the OrganisationParty Type) and FinancialIdentifier elements. Platform Operators by default would use the standard due diligence procedures, but can also rely on the GVS procedure where it has been made available by the Reportable Jurisdiction .
											The Standard element should be completed in case the Reporting Platform Operator has relied on the standard due diligence procedures of the OECD Model Rules or [EU Specific] [EU DIR2021/514] to identify and document the Entity Seller.</xsd:documentation>
										</xsd:annotation>
										<xsd:complexType>
											<xsd:sequence>
												<xsd:element name="EntSellerID" type="dpi:OrganisationParty_Type">
													<xsd:annotation>
														<xsd:documentation>The EntSellerID element identifies the Entity Seller and follows the OrganisationParty_Type.</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="FinancialIdentifier" type="dpi:FinancialIdentifier_Type" minOccurs="0" maxOccurs="unbounded">
													<xsd:annotation>
														<xsd:documentation>The Financial Identifier is an optional (mandatory) element that reflects the Financial Account Identifier which, under the OECD Model Rules or [EU Specific] [EU DIR2021/514], is the unique identifying number or reference available to the Reporting Platform Operator of the bank account or other payment account to which the Consideration is paid or credited. Under subparagraphs B(2)(c) and B(3)(c) of Section III of the OECD Model Rules or [EU Specific] subparagraphs B(2)(b) and B(3)(b) of Section III of [EU DIR2021/514], the Financial Identifier must be reported and exchanged provided that it is available to the Reporting Platform Operator and that the jurisdiction of the Reportable Seller’s residence has indicated that it wishes to receive such Financial Identifiers for taxpayer matching purposes.</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="PermanentEstablishments" type="dpi:PermanentEstablishments_Type" minOccurs="0">
													<xsd:annotation>
														<xsd:documentation>[EU Specific] This data element provides information on any permanent establishment through which Relevant Activities are carried out by the Entity Seller in the Union, indicating each respective EU Member State where such a permanent establishment is located.</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
											</xsd:sequence>
										</xsd:complexType>
									</xsd:element>
									<xsd:element name="GVS" type="dpi:GVSType">
										<xsd:annotation>
											<xsd:documentation>The GVS element should be completed in case the Reporting Platform Operator has relied on an approved Government Verification Service in order to identify and document the Entity Seller.</xsd:documentation>
										</xsd:annotation>
									</xsd:element>
								</xsd:choice>
							</xsd:complexType>
						</xsd:element>
						<xsd:element name="IndividualSeller">
							<xsd:complexType>
								<xsd:choice>
									<xsd:element name="Standard">
										<xsd:annotation>
											<xsd:documentation>The Standard element reflects the standard due diligence procedures and is further comprised of the IndSellerID element (which follows the PersonParty Type) and the Financial Identifier element. Platform Operators by default would use the standard due diligence procedures, but can also rely on the GVS procedure where it has been made available by the Reportable Jurisdiction .
											The PersonParty_Type, as enumerated in Section III of the OECD Model Rules or [EU Specific] [EU DIR2021/514], defines the information to be included in relation to an individual where the standard due diligence procedures are followed.</xsd:documentation>
										</xsd:annotation>
										<xsd:complexType>
											<xsd:sequence>
												<xsd:element name="IndSellerID" type="dpi:PersonParty_Type">
													<xsd:annotation>
														<xsd:documentation>The IndSellerID element identifies the individual Seller and follows the PersonParty_Type</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
												<xsd:element name="FinancialIdentifier" type="dpi:FinancialIdentifier_Type" minOccurs="0" maxOccurs="unbounded">
													<xsd:annotation>
														<xsd:documentation>The Financial Identifier is an optional (mandatory) and repeatable element that reflects the Financial Account Identifier which, under the OECD Model Rules or [EU Specific] [EU DIR2021/514], is the unique identifying number or reference available to the Reporting Platform Operator of the bank account or other payment account to which the Consideration is paid or credited. Under subparagraphs B(2)(c) and B(3)(c) of Section III of the OECD Model Rules or [EU Specific] subparagraphs B(2)(b) and B(3)(b) of Section III of [EU DIR2021/514], the Financial Identifier must be reported and exchanged provided that it is available to the Reporting Platform Operator and that the jurisdiction of the Reportable Seller’s residence has indicated that it wishes to receive such Financial Identifiers for taxpayer matching purposes.</xsd:documentation>
													</xsd:annotation>
												</xsd:element>
											</xsd:sequence>
										</xsd:complexType>
									</xsd:element>
									<xsd:element name="GVS" type="dpi:GVSType">
										<xsd:annotation>
											<xsd:documentation>The GVS element should be completed in case the Reporting Platform Operator has relied on an approved Government Verification Service in order to identify and document the Individual Seller.</xsd:documentation>
										</xsd:annotation>
									</xsd:element>
								</xsd:choice>
							</xsd:complexType>
						</xsd:element>
					</xsd:choice>
				</xsd:complexType>
			</xsd:element>
			<xsd:element name="RelevantActivities">
				<xsd:annotation>
					<xsd:documentation>The Relevant Activities element includes information on the Relevant Activities provided by Reportable Sellers and the Consideration derived therefrom. The element is comprised of the Immovable Property, Personal Services, sale  of Goods and Transportation Rental elements, reflecting the nature of the activities provided by the Reportable Seller. Under the OECD Model Rules and [EU DIR2021/514], information in respect of the Immovable Property and Personal Services elements must be provided. Under the OECD Extended Scope and [EU DIR2021/514], information in respect of the sale of Goods and Transportation Rental elements must also be provided.</xsd:documentation>
				</xsd:annotation>
				<xsd:complexType>
					<xsd:sequence>
						<xsd:element name="ImmovableProperty" minOccurs="0">
							<xsd:annotation>
								<xsd:documentation>The Immovable Property element contains information on Relevant Activities provided by a Reportable Seller in relation to the rental of immovable property. It comprises the repeatable Property Listing element.</xsd:documentation>
							</xsd:annotation>
							<xsd:complexType>
								<xsd:sequence>
									<xsd:element name="PropertyListing" type="dpi:PropertyListingType" maxOccurs="unbounded">
										<xsd:annotation>
											<xsd:documentation>The Property Listing element is comprised of the Address, Land Registration Number, Consideration, Number of Activities, Fees, Taxes, Property Type and Rented days elements.</xsd:documentation>
										</xsd:annotation>
									</xsd:element>
								</xsd:sequence>
							</xsd:complexType>
						</xsd:element>
						<xsd:element name="PersonalServices" type="dpi:OtherActivities_Type" minOccurs="0">
							<xsd:annotation>
								<xsd:documentation>The Personal Services element contains information on Relevant Activities involving time- or task-based work performed by one or more individuals, acting either independently or on behalf of an Entity, and which is carried out at the request of a user, either online or physically offline after having been facilitated via Platform.</xsd:documentation>
							</xsd:annotation>
						</xsd:element>
						<xsd:element name="SaleOfGoods" type="dpi:OtherActivities_Type" minOccurs="0">
							<xsd:annotation>
								<xsd:documentation>The sale of Goods element specifies the Relevant Activities provided by a Reportable Seller with respect to sale of goods.</xsd:documentation>
							</xsd:annotation>
						</xsd:element>
						<xsd:element name="TransportationRental" type="dpi:OtherActivities_Type" minOccurs="0">
							<xsd:annotation>
								<xsd:documentation>The Transportation Rental element specifies the Relevant Activities provided by a Reportable Seller with respect to the rental of any mode of transport.</xsd:documentation>
							</xsd:annotation>
						</xsd:element>
					</xsd:sequence>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!--  -->
	<!-- Permanent Establishments (Complex)-->
	<xsd:complexType name="PermanentEstablishments_Type">
		<xsd:sequence>
			<xsd:annotation>
				<xsd:documentation>The repeatable Permanent Establishment element identifies an EU Member State where a permanent establishment through which Relevant Activities are carried out by the Entity Seller in the Union is located.</xsd:documentation>
			</xsd:annotation>
			<xsd:element name="PermanentEstablishment" type="iso:MSCountryCode_Type" maxOccurs="unbounded"/>
		</xsd:sequence>
	</xsd:complexType>
	<!-- -->
	<!-- Other Platform Operators (Complex)-->
	<xsd:complexType name="OtherPlatformOperators_Type">
		<xsd:choice>
			<xsd:sequence>
				<xsd:element name="AssumingPlatformOperator" type="dpi:CorrectableOtherRPO_Type">
					<xsd:annotation>
						<xsd:documentation>This element provides information about the Platform Operator assuming the reporting in the name of the Reporting Platform Operator, as identified in the Platform Operator element.</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:sequence>
			<xsd:sequence>
				<xsd:element name="AssumedPlatformOperator" type="dpi:CorrectableOtherRPO_Type" maxOccurs="unbounded">
					<xsd:annotation>
						<xsd:documentation>This repeatable element provides information about each Platform Operator for which the Reporting Platform Operator, as identified in the Platform Operator element, assumes the reporting.</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:sequence>
		</xsd:choice>
	</xsd:complexType>
	<xsd:complexType name="OtherRPO_Type">
		<xsd:sequence>
			<xsd:annotation>
				<xsd:documentation>This element provides information about the assuming or assumed Platform Operator.</xsd:documentation>
			</xsd:annotation>
			<xsd:element name="ResCountryCode" type="iso:CountryCode_Type" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>This repeatable data element describes the residence country code(s) of the Platform Operator assuming the reporting or the residence country code(s) of each Platform Operator for which the Reporting Platform Operator assumes the reporting. As with the residence country code(s) of the Reporting Platform Operator, the residence country code of this Platform Operator should correspond to the jurisdiction where the Platform Operator is resident for tax purposes or, where it does not have a residence for tax purposes, either the jurisdiction it is incorporated under or the jurisdiction that it has its place of management (including effective management) in, [EU Specific] or the Member State where it has a permanent establishment in.
					[EU Specific] This element is optional for the purposes of [EU DIR2021/514].
</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="TIN" type="dpi:TIN_Type" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>This data element provides the tax identification number (TIN) used by the tax administration of the jurisdiction of residence of the entity. In case the entity does not have a TIN, or the TIN is not known to the sending Competent Authority, the Unknown attribute (see below) must be set to “true”. Furthermore, in case more than one TIN are provided, any provided element cannot be flagged as “unknown”.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Name" type="dpi:NameOrganisation_Type">
				<xsd:annotation>
					<xsd:documentation>This element should contain the legal name of the entity, including the domestic designation for the legal form, as indicated in its articles of incorporation or any similar document.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="Address" type="dpi:Address_Type">
				<xsd:annotation>
					<xsd:documentation>This data element should contain the address of the entity, including the country code of the address as well as the type of the address, indicating the legal character of that address.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- -->
	<!-- Correctable Other Platform Operator (Complex)-->
	<xsd:complexType name="CorrectableOtherRPO_Type">
		<xsd:complexContent>
			<xsd:extension base="dpi:OtherRPO_Type">
				<xsd:sequence>
					<xsd:element name="DocSpec" type="stf:DocSpec_Type">
						<xsd:annotation>
							<xsd:documentation>DocSpec identifies the particular report within the DPI message being transmitted. It permits the identification of reports requiring correction (for further guidance see the Corrections section below).</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<!--DPI Body Type - DPI Reporting  -->
	<xsd:complexType name="DPIBody_Type">
		<xsd:sequence>
			<xsd:annotation>
				<xsd:documentation>The DPI Body element contains the information on the Reporting Platform Operator, as well as on the Relevant Services performed by Reportable Sellers that give rise to the exchange.</xsd:documentation>
			</xsd:annotation>
			<xsd:element name="PlatformOperator" type="dpi:CorrectablePlatformOperator_Type">
				<xsd:annotation>
					<xsd:documentation>The Platform Operator element identifies the Reporting Platform Operator and follows the Organisation Party type (see OrganisationParty_Type).</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="OtherPlatformOperators" type="dpi:OtherPlatformOperators_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>This other Platform Operators element identifies either:
					•	The Platform Operator assuming the reporting in the name of the Reporting Platform Operator, as identified in the Platform Operator 		element;
					•	Each Platform Operator for which the Reporting Platform Operator, as identified in the Platform Operator element, assumes the reporting.
					[OECD Specific] This element must be provided, if available.
					[EU Specific] This element is optional for the purposes of [EU DIR2021/514].
</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="ReportableSeller" type="dpi:CorrectableReportableSeller_Type" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>This element contains the identification information on each Reportable Seller, as well as information on the Relevant Services provided by such Reportable Seller and the Consideration derived therefrom.
					In case of Nil Reporting, i.e. when MessageTypeIndic is set to “DPI403”, no Reportable Seller must be provided.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!--  -->
	<!--+++++++++++++++++++++++++++++++++++++++++++++++++++++++++ Schema  element ++++++++++++++++++++++++++++++++++++++++++++ -->
	<!-- DPI OECD File Message structure  -->
	<!-- -->
	<!--DPI Message structure  -->
	<xsd:element name="DPI_OECD">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="MessageSpec" type="dpi:MessageSpec_Type">
					<xsd:annotation>
						<xsd:documentation>The information in the message header identifies the tax administration that is sending the DPI message. It specifies when the message was created, what reporting period the report is for, and the nature of the report (original, supplemental, etc.).</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:element name="DPIBody" type="dpi:DPIBody_Type" minOccurs="0" maxOccurs="unbounded">
					<xsd:annotation>
						<xsd:documentation>The DPI Body element contains the information on the Reporting Platform Operator, as well as on the Relevant Services performed by Reportable Sellers that give rise to the exchange.</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="version" type="stf:StringMin1Max10_Type">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">The DPI Version.</xsd:documentation>
				</xsd:annotation>
			</xsd:attribute>
		</xsd:complexType>
	</xsd:element>
	<!--  -->
</xsd:schema>
