<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2013 rel. 2 (x64) (http://www.altova.com) by Accounting Division (OECD) -->
<xsd:schema xmlns:stf="urn:oecd:ties:dpistf:v1" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:iso="urn:oecd:ties:isodpitypes:v1" targetNamespace="urn:oecd:ties:dpistf:v1" elementFormDefault="qualified" attributeFormDefault="unqualified" version="1.0">
	<xsd:import namespace="urn:oecd:ties:isodpitypes:v1" schemaLocation="isodpitypes_v1.0.xsd"/>
	<!-- -->
	<!--+++++++++++++++++++++++  String lenght types ++++++++++++++++++++++++++++++++++++++ -->
	<!-- -->
	<!-- Defines a string with minimum length 1 and maximum length of 10 -->
	<xsd:simpleType name="StringMin1Max10_Type">
		<xsd:annotation>
			<xsd:documentation>Defines a string with minimum length 1 and maximum length of 10.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="10"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- -->
	<!-- Defines a string with minimum length 1 and maximum length of 170 -->
	<xsd:simpleType name="StringMin1Max170_Type">
		<xsd:annotation>
			<xsd:documentation>Defines a string with minimum length 1 and maximum length of 170.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="170"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- -->
	<!-- Defines a string with minimum length 1 and maximum length of 200 -->
	<xsd:simpleType name="StringMin1Max200_Type">
		<xsd:annotation>
			<xsd:documentation>Defines a string with minimum length 1 and maximum length of 200.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="200"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- -->
	<!-- Defines a string with minimum length 0 and maximum length of 200 -->
	<xsd:simpleType name="StringMin0Max200_Type">
		<xsd:annotation>
			<xsd:documentation>Defines a string with minimum length 0 and maximum length of 200.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="0"/>
			<xsd:maxLength value="200"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- -->
	<!-- Defines a string with minimum length 1 and maximum length of 400 -->
	<xsd:simpleType name="StringMin1Max400_Type">
		<xsd:annotation>
			<xsd:documentation>Defines a string with minimum length 1 and maximum length of 400.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="400"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- -->
	<!-- Defines a string with minimum length 1 and maximum length of 4000 -->
	<xsd:simpleType name="StringMin1Max4000_Type">
		<xsd:annotation>
			<xsd:documentation>Defines a string with minimum length 1 and maximum length of 4000.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="4000"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- -->
	<!-- Defines a string with minimum length 1 and maximum length of 4000, with the Language attribute -->
	<xsd:complexType name="StringMin1Max4000WithLang_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">Defines a string with minimum length 1 and maximum length of 4000, with the Language attribute.</xsd:documentation>
		</xsd:annotation>
		<xsd:simpleContent>
			<xsd:extension base="stf:StringMin1Max4000_Type">
				<xsd:attribute name="language" type="iso:LanguageCode_Type" use="optional">
					<xsd:annotation>
						<xsd:documentation xml:lang="en">Language used</xsd:documentation>
					</xsd:annotation>
				</xsd:attribute>
			</xsd:extension>
		</xsd:simpleContent>
	</xsd:complexType>
	<!-- -->
	<!--+++++++++++++++++++++++  Reusable Simple types ++++++++++++++++++++++++++++++++++++++ -->
	<!-- Document type indicators types -->
	<xsd:simpleType name="OECDDocTypeIndic_EnumType">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">A message can either contain new records (OECD1) or corrections and/or deletions (OECD2 and/or OECD3), but should not contain a mixture of both. The resend option (OECD 0) can only be used for the Reporting Platform Operator element when the Platform Operator element has already been sent. The resend option (OECD 0) can be used in the two following cases: 
-	New data: in case new Reportable Seller information is provided in respect of a Reportable Period and the Platform Operator element has already been sent;
-	Correction/deletion: in case the Reportable Seller element is corrected (or deleted) and the Platform Operator element has already been sent and the Platform Operator element does not need to be corrected (or deleted). The Platform Operator cannot be deleted without deleting all related Reportable Seller information (either in same message or in previous messages). For a correction message, in case the information in the Platform Operator element is not altered, such uncorrected element may be left blank.
			This element specifies the type of data being submitted.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="OECD0">
				<xsd:annotation>
					<xsd:documentation>Resent Data</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OECD1">
				<xsd:annotation>
					<xsd:documentation>New Data</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OECD2">
				<xsd:annotation>
					<xsd:documentation>Corrected Data</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OECD3">
				<xsd:annotation>
					<xsd:documentation>Deletion of Data</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OECD10">
				<xsd:annotation>
					<xsd:documentation>Resent Test Data</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OECD11">
				<xsd:annotation>
					<xsd:documentation>New Test Data</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OECD12">
				<xsd:annotation>
					<xsd:documentation>Corrected Test Data</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OECD13">
				<xsd:annotation>
					<xsd:documentation>Deletion of Test Data</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- -->
	<!-- Kind of Name -->
	<xsd:simpleType name="OECDNameType_EnumType">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">It is possible for stf documents to contain several names for the same party. This is a qualifier to indicate the type of a particular name. Such types include nicknames ('nick'), names under which a party does business ('dba' a short name for the entity, or a name that is used for public acquaintance instead of the official business name) etc.
			</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="OECD201">
				<xsd:annotation>
					<xsd:documentation>SMFAliasOrOther</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OECD202">
				<xsd:annotation>
					<xsd:documentation>indiv (individual)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OECD203">
				<xsd:annotation>
					<xsd:documentation>alias (alias)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OECD204">
				<xsd:annotation>
					<xsd:documentation>nick (nickname)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OECD205">
				<xsd:annotation>
					<xsd:documentation>aka (also known as)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OECD206">
				<xsd:annotation>
					<xsd:documentation>dba (doing business as)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OECD207">
				<xsd:annotation>
					<xsd:documentation>legal (legal name)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OECD208">
				<xsd:annotation>
					<xsd:documentation>atbirth (name at birth)</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- -->
	<!-- Type of the address considered from a legal point of view -->
	<xsd:simpleType name="OECDLegalAddressType_EnumType">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">This is a datatype for an attribute to an address. It serves to indicate the legal character of that address (residential, business etc.)</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:token">
			<xsd:enumeration value="OECD301">
				<xsd:annotation>
					<xsd:documentation>residentialOrBusiness</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OECD302">
				<xsd:annotation>
					<xsd:documentation>residential</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OECD303">
				<xsd:annotation>
					<xsd:documentation>business</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OECD304">
				<xsd:annotation>
					<xsd:documentation>registeredOffice</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="OECD305">
				<xsd:annotation>
					<xsd:documentation>unspecified</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- -->
	<!--++++++++++++++++++ Reusable Complex types +++++++++++++++++++++++++++++++++++++ -->
	<!-- -->
	<!-- Document specification: Data identifying and describing the document -->
	<xsd:complexType name="DocSpec_Type">
		<xsd:annotation>
			<xsd:documentation xml:lang="en">Document specification: Data identifying and describing the document, where
'document' here means the part of a message that is to transmit the data about a single block of DPI information. </xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="DocTypeIndic" type="stf:OECDDocTypeIndic_EnumType"/>
			<xsd:element name="DocRefId" type="stf:StringMin1Max200_Type">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">Sender's unique identifier of this document.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CorrMessageRefId" type="stf:StringMin1Max170_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">This element must not be used.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CorrDocRefId" type="stf:StringMin1Max200_Type" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation xml:lang="en">Reference id of the document referred to if this is correction.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
</xsd:schema>
