package hero.api.messages.controller.dto

import hero.api.user.controller.dto.UserResponse
import hero.contract.api.dto.PostResponse
import hero.core.data.SimplePageResponse
import hero.model.SubscriptionRelationType
import java.time.Instant

data class PagedMessageThreadsResponse(
    override val content: List<MessageThreadResponse>,
    override val hasNext: Boolean,
    override val afterCursor: String? = null,
    override val beforeCursor: String? = null,
) : SimplePageResponse<MessageThreadResponse>

data class MessageThreadResponse(
    val id: String,
    val participants: List<UserResponse>,
    val participantIds: Set<String>,
    val createdAt: Instant?,
    val seenAt: Instant?,
    val checkedAt: Instant?,
    val lastMessageAt: Instant?,
    val canMessage: Boolean,
    val deleted: Boolean = false,
    val archived: Boolean = false,
    val deletedAt: Instant? = null,
    val lastMessage: PostResponse,
)

data class MessageThreadDetailsResponse(
    val id: String,
    val participants: List<UserResponse>,
    val participantIds: List<String>,
    val createdAt: Instant?,
    val canPost: Boolean,
    val relation: SubscriptionRelationType,
    val commonCreators: List<String> = emptyList(),
    val seenAt: Instant?,
    val checkedAt: Instant?,
    val lastMessageAt: Instant?,
    val lastMessageId: String?,
    val deleted: Boolean = false,
    val archived: Boolean = false,
    val deletedAt: Instant? = null,
)

data class CreateMessageRequest(
    val text: String,
)
