package hero.api.subscriber.controller.dto

import hero.api.user.controller.dto.exampleUserResponse
import hero.baseutils.plusDays
import hero.model.CouponMethod
import hero.model.Currency
import hero.model.SubscriberType
import hero.model.SubscriptionsDtoStatus
import java.time.Instant

val exampleSubscriptionDetailsResponse = SubscriptionDetailsResponse(
    status = SubscriptionsDtoStatus.ACTIVE,
    cancelAtPeriodEnd = false,
    expires = Instant.now().plusDays(10),
    couponAppliedForMonths = 3,
    couponAppliedForDays = 7,
    couponExpiresAt = Instant.now(),
    tierId = "EUR05",
    type = SubscriberType.STRIPE,
    couponMethod = CouponMethod.VOUCHER,
    couponPercentOff = 30L,
)

val exampleTierResponse = TierResponse(
    id = "EUR05",
    priceCents = 500,
    currency = Currency.EUR,
    default = true,
    hidden = false,
)

val exampleSubscriptionResponse = SubscriptionResponse(
    id = "subscriber-id",
    subscribedAt = Instant.now(),
    details = exampleSubscriptionDetailsResponse,
    subscriber = exampleUserResponse,
    creator = exampleUserResponse,
    tier = exampleTierResponse,
)

val examplePagedSubscriptionResponse = PagedSubscriptionResponse(
    content = listOf(exampleSubscriptionResponse),
    hasNext = false,
    afterCursor = "eyJsYXN0UHVibGlzaGVkQXQiOiIyMDIzLTA5LTA1VDA4OjQzOjEwLjYxNjQzMVoifQ==",
)

val exampleSubscribeRequestResponse = SubscribeRequestResponse(
    id = 1L,
    userId = "user-id",
    creatorId = "creator-id",
    createdAt = Instant.now(),
    acceptedAt = Instant.now(),
    declinedAt = Instant.now(),
    deletedAt = Instant.now(),
    seenAt = Instant.now(),
)

val examplePagedSubscribeRequestResponse = PagedSubscribeRequestResponse(
    content = listOf(exampleSubscribeRequestResponse),
    hasNext = false,
    afterCursor = "eyJsYXN0Q3JlYXRlZEF0IjoiMjAyMy0wOS0wNVQwODo0MzoxMC42MTY0MzFaIn0=",
)

val exampleGetSubscribeRequestResponse = GetSubscribeRequestResponse(
    data = exampleSubscribeRequestResponse,
)
