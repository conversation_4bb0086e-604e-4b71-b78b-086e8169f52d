package hero.api.community.controller

import hero.api.community.controller.dto.exampleCommunityResponse
import hero.api.community.controller.dto.toResponse
import hero.api.community.service.CommunityCommandService
import hero.api.community.service.CommunityQueryService
import hero.api.community.service.CreateCommunity
import hero.api.community.service.GetCommunity
import hero.api.user.service.GetUser
import hero.api.user.service.UserQueryService
import hero.http4k.auth.getJwtUser
import hero.http4k.auth.parseJwtUser
import hero.http4k.extensions.authorization
import hero.http4k.extensions.body
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.post
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header
import org.http4k.lens.Path
import org.http4k.lens.Query
import org.http4k.lens.boolean
import org.http4k.lens.string
import java.util.UUID

class CommunitiesController(
    private val communityCommandService: CommunityCommandService,
    private val communityQueryService: CommunityQueryService,
    private val userQueryService: UserQueryService,
) {
    @Suppress("unused")
    val routePostCommunities: ContractRoute =
        ("/v1/communities").post(
            summary = "Create new community.",
            tag = "Communities",
            parameters = object {
                val authorization = Header.authorization()
            },
            receiving = Unit,
            responses = listOf(Status.OK example exampleCommunityResponse),
            handler = { request, _ ->
                val jwtUser = request.getJwtUser()

                val community = communityCommandService.execute(CreateCommunity(jwtUser.id))
                val user = userQueryService.execute(GetUser(jwtUser.id))

                Response(Status.OK).body(community.toResponse(user.user, true))
            },
        )

    @Suppress("unused")
    val routeGetCommunities: ContractRoute =
        ("/v1/communities" / Path.string().of("communityId or slug")).get(
            summary = "Create new community.",
            tag = "Communities",
            parameters = object {
                val isSlug = Query.boolean().defaulted("isSlug", false, "Is the provided id a slug.")
                val authorization = Header.authorization()
            },
            responses = listOf(Status.OK example exampleCommunityResponse),
            handler = { request, parameters, id ->
                val jwtUser = request.parseJwtUser()
                val isSlug = parameters.isSlug(request)
                val (communityId, slug) = if (isSlug) {
                    null to id
                } else {
                    UUID.fromString(id) to null
                }
                val result = communityQueryService.execute(GetCommunity(communityId, slug, jwtUser?.id))
                val user = userQueryService.execute(GetUser(result.community.ownerId))

                Response(Status.OK).body(result.community.toResponse(user.user, result.isMember))
            },
        )
}
