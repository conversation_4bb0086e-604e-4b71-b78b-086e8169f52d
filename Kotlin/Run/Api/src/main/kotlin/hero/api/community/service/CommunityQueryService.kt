package hero.api.community.service

import hero.exceptions.http.BadRequestException
import hero.exceptions.http.NotFoundException
import hero.model.Community
import hero.repository.community.CommunityRepository
import hero.repository.community.JooqCommunityHelper
import hero.repository.community.fetchCommunityIds
import hero.sql.jooq.Tables.COMMUNITY
import org.jooq.DSLContext
import org.jooq.exception.NoDataFoundException
import java.util.UUID

class CommunityQueryService(
    lazyContext: Lazy<DSLContext>,
    private val communityRepository: CommunityRepository,
) {
    private val context by lazyContext

    fun execute(query: GetCommunity): CommunityWithMeta {
        if (query.communityId == null && query.slug == null) {
            throw BadRequestException("Either communityId or slug must be provided")
        }

        if (query.communityId != null && query.slug != null) {
            throw BadRequestException("Only one of communityId or slug must be provided")
        }

        val communityIds = query.userId?.let { context.fetchCommunityIds(it) } ?: emptySet()

        val community = if (query.communityId != null) {
            communityRepository.getById(query.communityId)
        } else {
            context.select(JooqCommunityHelper.communityFields)
                .from(COMMUNITY)
                .where(COMMUNITY.SLUG.eq(query.slug))
                .runCatching { JooqCommunityHelper.mapRecordToEntity(fetchSingle()) }
                .getOrElse {
                    when (it) {
                        is NoDataFoundException -> throw NotFoundException()
                        else -> throw it
                    }
                }
        }

        return CommunityWithMeta(community, community.id in communityIds)
    }
}

data class GetCommunity(val communityId: UUID?, val slug: String?, val userId: String?)

data class CommunityWithMeta(val community: Community, val isMember: Boolean)
