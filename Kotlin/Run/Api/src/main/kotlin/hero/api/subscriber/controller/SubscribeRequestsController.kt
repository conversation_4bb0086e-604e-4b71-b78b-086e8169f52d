package hero.api.subscriber.controller

import hero.api.subscriber.controller.dto.CreateSubscribeRequestRequest
import hero.api.subscriber.controller.dto.GetSubscribeRequestResponse
import hero.api.subscriber.controller.dto.UpdateSubscribeRequest
import hero.api.subscriber.controller.dto.UpdateSubscribeRequestType
import hero.api.subscriber.controller.dto.exampleGetSubscribeRequestResponse
import hero.api.subscriber.controller.dto.examplePagedSubscribeRequestResponse
import hero.api.subscriber.controller.dto.toResponse
import hero.api.subscriber.service.AcceptSubscribeRequest
import hero.api.subscriber.service.CancelSubscribeRequest
import hero.api.subscriber.service.DeclineSubscribeRequest
import hero.api.subscriber.service.FindLastSubscribeRequest
import hero.api.subscriber.service.GetSubscribeRequests
import hero.api.subscriber.service.MarkSubscribeRequestsAsSeen
import hero.api.subscriber.service.RequestToSubscribe
import hero.api.subscriber.service.SubscribeRequestCommandService
import hero.api.subscriber.service.SubscribeRequestQueryService
import hero.core.data.PageRequest
import hero.core.data.toResponse
import hero.exceptions.http.ForbiddenException
import hero.http4k.auth.getJwtUser
import hero.http4k.controller.QueryUtils
import hero.http4k.controller.QueryUtils.userId
import hero.http4k.extensions.authorization
import hero.http4k.extensions.body
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.http4k.extensions.put
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header
import org.http4k.lens.Path
import org.http4k.lens.long

class SubscribeRequestsController(
    private val subscribeRequestCommandService: SubscribeRequestCommandService,
    private val subscribeRequestQueryService: SubscribeRequestQueryService,
) {
    @Suppress("unused")
    val routeAcceptSubscribeRequest: ContractRoute =
        ("/v1/subscribe-requests" / Path.long().of("id")).put(
            summary = "Update the subscribe request",
            tag = "Subscribe Requests",
            parameters = object {},
            responses = listOf(Status.NO_CONTENT to Unit),
            receiving = UpdateSubscribeRequest(UpdateSubscribeRequestType.ACCEPT),
            handler = { request, _, id ->
                val user = request.getJwtUser()
                val body = lens<UpdateSubscribeRequest>(request)

                when (body.type) {
                    UpdateSubscribeRequestType.ACCEPT ->
                        subscribeRequestCommandService.execute(
                            AcceptSubscribeRequest(
                                userId = user.id,
                                requestId = id,
                            ),
                        )

                    UpdateSubscribeRequestType.DELETE ->
                        subscribeRequestCommandService.execute(
                            CancelSubscribeRequest(
                                userId = user.id,
                                requestId = id,
                            ),
                        )

                    UpdateSubscribeRequestType.DECLINE ->
                        subscribeRequestCommandService.execute(
                            DeclineSubscribeRequest(
                                userId = user.id,
                                requestId = id,
                            ),
                        )
                }

                Response(Status.NO_CONTENT)
            },
        )

    @Suppress("unused")
    val routeCreateSubscribeRequest: ContractRoute =
        "/v1/subscribe-requests".post(
            summary = "Create a subscribe request",
            tag = "Subscribe Requests",
            parameters = object {},
            responses = listOf(Status.CREATED to Unit),
            receiving = CreateSubscribeRequestRequest("creator-id"),
            handler = { request, _ ->
                val user = request.getJwtUser()
                val body = lens<CreateSubscribeRequestRequest>(request)

                subscribeRequestCommandService.execute(
                    RequestToSubscribe(
                        creatorId = body.creatorId,
                        userId = user.id,
                    ),
                )

                Response(Status.CREATED)
            },
        )

    @Suppress("unused")
    val routeGetSubscribeRequests: ContractRoute =
        "/v1/subscribe-requests".get(
            summary = "Get subscribe requests for the authenticated user",
            tag = "Subscribe Requests",
            parameters = object {
                val pageSize = QueryUtils.pageSize()
                val afterCursor = QueryUtils.afterCursor()
            },
            responses = listOf(Status.OK to examplePagedSubscribeRequestResponse),
            handler = { request, parameters ->
                val user = request.getJwtUser()
                val afterCursor = parameters.afterCursor(request)
                val pageSize = parameters.pageSize(request)

                val pageRequest = PageRequest(pageSize = pageSize, afterCursor = afterCursor)
                val result = subscribeRequestQueryService.execute(
                    GetSubscribeRequests(
                        userId = user.id,
                        pageable = pageRequest,
                    ),
                )

                val response = result.toResponse { it.toResponse() }

                Response(Status.OK).body(response)
            },
        )

    @Suppress("unused")
    val routeGetSubscribeRequestsForCreator: ContractRoute =
        ("/v1/subscribe-requests" / Path.of("creator-id")).get(
            summary = "Get subscribe requests for the authenticated user sent to given creator",
            tag = "Subscribe Requests",
            parameters = object {
            },
            responses = listOf(Status.OK to exampleGetSubscribeRequestResponse),
            handler = { request, _, creatorId ->
                val user = request.getJwtUser()

                val result = subscribeRequestQueryService.execute(
                    FindLastSubscribeRequest(
                        creatorId = creatorId,
                        userId = user.id,
                    ),
                )

                Response(Status.OK).body(GetSubscribeRequestResponse(data = result?.toResponse()))
            },
        )

    @Suppress("unused")
    val routePostMarkSeen: ContractRoute =
        ("/v1/users" / Path.userId().of("userId") / "mark-subscribe-requests-seen").post(
            summary = "Mark all user's subscribe requests as seen.",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(Status.NO_CONTENT example Unit),
            receiving = null,
            tag = "Subscribe Requests",
            handler = { request, _, userId, _ ->
                val user = request.getJwtUser(allowImpersonation = false)
                if (user.id != userId) {
                    throw ForbiddenException(
                        "Cannot mark notifications of different user as seen: ${user.id} != $userId.",
                    )
                }

                subscribeRequestCommandService.execute(MarkSubscribeRequestsAsSeen(user.id))
                Response(Status.NO_CONTENT)
            },
        )
}
