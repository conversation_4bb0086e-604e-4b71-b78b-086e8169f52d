package hero.functions

import hero.model.SupportCounts
import hero.model.topics.PostState
import hero.model.topics.PostStateChange
import hero.model.topics.PostStateChanged
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Instant
import java.util.UUID

class UserPostCounterIT : IntegrationTest() {

    @Test
    fun `should update user post count and lastPostAt when post is published`() {
        val underTest = UserPostCounter(
            lazyTestContext,
            firestore,
            TestCollections.usersCollection,
            TestCollections.categoriesCollection,
            TestCollections.messageThreadsCollection
        )

        val userId = "test-user-${UUID.randomUUID()}"
        testHelper.createUser(userId, counts = SupportCounts(posts = 0))

        val post = testHelper.createPost(userId = userId, state = PostState.PUBLISHED)

        underTest.consume(PostStateChanged(stateChange = PostStateChange.PUBLISHED, post = post))

        val updatedUser = TestCollections.usersCollection[userId].get()
        assertThat(updatedUser.counts.posts).isEqualTo(1)
        assertThat(updatedUser.lastPostAt).isNotNull()
    }

    @Test
    fun `should not count comments as posts`() {
        val underTest = UserPostCounter(lazyTestContext, firestore)

        val userId = "test-user-${UUID.randomUUID()}"

        with(testHelper) {
            createUser(userId, counts = SupportCounts(posts = 0))
            val parentPost = createPost(userId = userId, state = PostState.PUBLISHED)
        }

        val comment = testHelper.createPost(
            userId = userId,
            parentId = "parent-post-id",
            state = PostState.PUBLISHED,
        )

        val postStateChanged = PostStateChanged(
            stateChange = PostStateChange.PUBLISHED,
            post = comment,
        )

        underTest.consume(postStateChanged)

        val user = TestCollections.usersCollection[userId].get()
        assertThat(user.counts.posts).isEqualTo(1) // Only the parent post should be counted
        assertThat(user.lastPostAt).isNotNull() // Should be set from the parent post
    }

    @Test
    fun `should update category post count when post is published`() {
        val underTest = UserPostCounter(lazyTestContext, firestore)

        val userId = "test-user-${UUID.randomUUID()}"
        val categoryId = "test-category-${UUID.randomUUID()}"

        with(testHelper) {
            createUser(userId)
            createCategory(userId, id = categoryId)
        }

        val post = testHelper.createPost(
            userId = userId,
            state = PostState.PUBLISHED,
            categories = listOf(categoryId),
        )

        val postStateChanged = PostStateChanged(
            stateChange = PostStateChange.PUBLISHED,
            post = post,
        )

        underTest.consume(postStateChanged)

        val updatedCategory = TestCollections.categoriesCollection[categoryId].get()
        assertThat(updatedCategory.postCount).isEqualTo(1)
    }

    @Test
    fun `should handle message thread post counts correctly`() {
        val underTest = UserPostCounter(lazyTestContext, firestore)

        val userId = "test-user-${UUID.randomUUID()}"
        val participantId = "participant-${UUID.randomUUID()}"
        val messageThreadId = "thread-${UUID.randomUUID()}"

        with(testHelper) {
            createUser(userId)
            createUser(participantId)

            val messageThread = createMessageThread(
                userId = userId,
                participants = listOf(participantId),
                id = messageThreadId,
            )
            TestCollections.messageThreadsCollection[messageThreadId].set(messageThread)
        }

        val post = testHelper.createPost(
            userId = userId,
            messageThreadId = messageThreadId,
            state = PostState.PUBLISHED,
        )

        val postStateChanged = PostStateChanged(
            stateChange = PostStateChange.PUBLISHED,
            post = post,
        )

        underTest.consume(postStateChanged)

        val updatedMessageThread = TestCollections.messageThreadsCollection[messageThreadId].get()
        assertThat(updatedMessageThread.posts).isEqualTo(1)
        assertThat(updatedMessageThread.lastMessageAt).isEqualTo(post.created)
        assertThat(updatedMessageThread.lastMessageBy).isEqualTo(userId)
        assertThat(updatedMessageThread.lastMessageId).isEqualTo(post.id)
        assertThat(updatedMessageThread.emailNotified).isFalse()
    }

    @Test
    fun `should update message thread seens and checks for post author`() {
        val underTest = UserPostCounter(lazyTestContext, firestore)

        val userId = "test-user-${UUID.randomUUID()}"
        val participantId = "participant-${UUID.randomUUID()}"
        val messageThreadId = "thread-${UUID.randomUUID()}"

        with(testHelper) {
            createUser(userId)
            createUser(participantId)

            val oldTime = Instant.now().minusSeconds(3600)
            val messageThread = createMessageThread(
                userId = userId,
                participants = listOf(participantId),
                id = messageThreadId,
            ).copy(
                seens = mapOf(userId to oldTime, participantId to oldTime),
                checks = mapOf(userId to oldTime, participantId to oldTime),
            )
            TestCollections.messageThreadsCollection[messageThreadId].set(messageThread)
        }

        val post = testHelper.createPost(
            userId = userId,
            messageThreadId = messageThreadId,
            state = PostState.PUBLISHED,
            createdAt = Instant.now(),
        )

        val postStateChanged = PostStateChanged(
            stateChange = PostStateChange.PUBLISHED,
            post = post,
        )

        underTest.consume(postStateChanged)

        val updatedMessageThread = TestCollections.messageThreadsCollection[messageThreadId].get()
        assertThat(updatedMessageThread.seens[userId]).isEqualTo(post.created)
        assertThat(updatedMessageThread.checks[userId]).isEqualTo(post.created)
    }

    @Test
    fun `should count only published posts for user`() {
        val underTest = UserPostCounter(lazyTestContext, firestore)

        val userId = "test-user-${UUID.randomUUID()}"

        with(testHelper) {
            createUser(userId, counts = SupportCounts(posts = 0))

            // Create posts with different states
            createPost(userId = userId, state = PostState.PUBLISHED)
            createPost(userId = userId, state = PostState.SCHEDULED)
            createPost(userId = userId, state = PostState.DELETED)
            createPost(userId = userId, state = PostState.PROCESSING)
        }

        val newPost = testHelper.createPost(
            userId = userId,
            state = PostState.PUBLISHED,
        )

        val postStateChanged = PostStateChanged(
            stateChange = PostStateChange.PUBLISHED,
            post = newPost,
        )

        underTest.consume(postStateChanged)

        val updatedUser = TestCollections.usersCollection[userId].get()
        // Should count the 2 published posts (one from setup + one from event)
        assertThat(updatedUser.counts.posts).isEqualTo(2)
    }

    @Test
    fun `should not update lastPostAt for non-published posts`() {
        val underTest = UserPostCounter(lazyTestContext, firestore)

        val userId = "test-user-${UUID.randomUUID()}"

        with(testHelper) {
            createUser(userId, lastPostAt = null)
        }

        val post = testHelper.createPost(
            userId = userId,
            state = PostState.DELETED,
        )

        val postStateChanged = PostStateChanged(
            stateChange = PostStateChange.DELETED,
            post = post,
        )

        underTest.consume(postStateChanged)

        val updatedUser = TestCollections.usersCollection[userId].get()
        assertThat(updatedUser.lastPostAt).isNull()
    }

    @Test
    fun `should handle multiple categories for a single post`() {
        val underTest = UserPostCounter(lazyTestContext, firestore)

        val userId = "test-user-${UUID.randomUUID()}"
        val categoryId1 = "category-1-${UUID.randomUUID()}"
        val categoryId2 = "category-2-${UUID.randomUUID()}"

        with(testHelper) {
            createUser(userId)
            createCategory(userId, id = categoryId1)
            createCategory(userId, id = categoryId2)
        }

        val post = testHelper.createPost(
            userId = userId,
            state = PostState.PUBLISHED,
            categories = listOf(categoryId1, categoryId2),
        )

        val postStateChanged = PostStateChanged(
            stateChange = PostStateChange.PUBLISHED,
            post = post,
        )

        underTest.consume(postStateChanged)

        listOf(categoryId1, categoryId2).forEach { categoryId ->
            val updatedCategory = TestCollections.categoriesCollection[categoryId].get()
            assertThat(updatedCategory.postCount).isEqualTo(1)
        }
    }

    @Test
    fun `should not update message thread lastMessageAt if post is older`() {
        val underTest = UserPostCounter(lazyTestContext, firestore)

        val userId = "test-user-${UUID.randomUUID()}"
        val participantId = "participant-${UUID.randomUUID()}"
        val messageThreadId = "thread-${UUID.randomUUID()}"
        val recentTime = Instant.now()
        val olderTime = recentTime.minusSeconds(3600)

        with(testHelper) {
            createUser(userId)
            createUser(participantId)

            val messageThread = createMessageThread(
                userId = userId,
                participants = listOf(participantId),
                id = messageThreadId,
                lastMessageAt = recentTime,
            ).copy(
                lastMessageBy = participantId,
                lastMessageId = "existing-message-id",
            )
            TestCollections.messageThreadsCollection[messageThreadId].set(messageThread)
        }

        val post = testHelper.createPost(
            userId = userId,
            messageThreadId = messageThreadId,
            state = PostState.PUBLISHED,
            createdAt = olderTime,
        )

        val postStateChanged = PostStateChanged(
            stateChange = PostStateChange.PUBLISHED,
            post = post,
        )

        underTest.consume(postStateChanged)

        val updatedMessageThread = TestCollections.messageThreadsCollection[messageThreadId].get()
        assertThat(updatedMessageThread.posts).isEqualTo(1)
        assertThat(updatedMessageThread.lastMessageAt).isEqualTo(recentTime) // Should not change
        assertThat(updatedMessageThread.lastMessageBy).isEqualTo(participantId) // Should not change
        assertThat(updatedMessageThread.lastMessageId).isEqualTo("existing-message-id") // Should not change
    }

    @Test
    fun `should exclude community posts from user post count`() {
        val underTest = UserPostCounter(lazyTestContext, firestore)

        val userId = "test-user-${UUID.randomUUID()}"
        val communityId = UUID.randomUUID()

        with(testHelper) {
            createUser(userId, counts = SupportCounts(posts = 0))
        }

        val post = testHelper.createPost(
            userId = userId,
            state = PostState.PUBLISHED,
            communityId = communityId,
        )

        val postStateChanged = PostStateChanged(
            stateChange = PostStateChange.PUBLISHED,
            post = post,
        )

        underTest.consume(postStateChanged)

        val updatedUser = TestCollections.usersCollection[userId].get()
        // Community posts should not be counted in user post count
        assertThat(updatedUser.counts.posts).isEqualTo(0)
    }

    @Test
    fun `should handle posts without categories`() {
        val underTest = UserPostCounter(lazyTestContext, firestore)

        val userId = "test-user-${UUID.randomUUID()}"

        with(testHelper) {
            createUser(userId, counts = SupportCounts(posts = 0))
        }

        val post = testHelper.createPost(
            userId = userId,
            state = PostState.PUBLISHED,
            categories = emptyList(),
        )

        val postStateChanged = PostStateChanged(
            stateChange = PostStateChange.PUBLISHED,
            post = post,
        )

        underTest.consume(postStateChanged)

        val updatedUser = TestCollections.usersCollection[userId].get()
        assertThat(updatedUser.counts.posts).isEqualTo(1)
        assertThat(updatedUser.lastPostAt).isNotNull()
    }

    private fun prepareFunction(): UserPostCounter {
        return UserPostCounter(
            lazyContext = lazyTestContext,
            firestore = firestore,
            usersCollection = TestCollections.usersCollection,
            categoriesCollection = TestCollections.categoriesCollection,
            messageThreadsCollection = TestCollections.messageThreadsCollection,
            clock = TestClock(expectedTimestamp),
            logger = TestLogger,
            envVariables = TestEnvironmentVariables,
        )
    }
}
