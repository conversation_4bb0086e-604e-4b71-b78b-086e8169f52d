Kotlin/Function/User/UserPostCounter/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Core/build
    - Kotlin/Modules/SQL/build
    - Kotlin/Modules/IntegrationTesting/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/User/UserPostCounter/variables:
  variables:
    FUNCTION_NAME: "user-post-counter"
    CLASS_NAME: "hero.functions.UserPostCounter"
    TOPIC: "PostStateChanged"

Kotlin/Function/User/UserPostCounter/deploy-devel:
  needs:
    - Kotlin/Function/User/UserPostCounter/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/User/UserPostCounter/variables

Kotlin/Function/User/UserPostCounter/deploy-staging:
  needs:
    - Kotlin/Function/User/UserPostCounter/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-staging
    - .Kotlin/Function/User/UserPostCounter/variables

Kotlin/Function/User/UserPostCounter/deploy-prod:
  needs:
    - Kotlin/Function/User/UserPostCounter/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/User/UserPostCounter/variables
