stages:
  - gitlab
  - build-modules
  - build-services
  - deploy-devel
  - deploy-staging
  - deploy-prod
  - terraform

include:
  - "/NodeJs/.gitlab-ci.yml"
  - "/Kotlin/.gitlab-ci.yml"
  - "/GitLab/.gitlab-ci.yml"
  - "/Terraform/.gitlab-ci.yml"

variables: &variables-list
  CLOUD_REGION: europe-west1
  CLOUD_ZONE: europe-west1-b
  # allows jobs to use `docker manifest …` commands
  DOCKER_CLI_EXPERIMENTAL: enabled
  SQL_INSTANCE_NAME: heroheroco:europe-west1:postgresql-instance

.variables-build-list: &variables-build-list
  <<: *variables-list
  PRODUCTION: "false"
  ENVIRONMENT: "devel"
  HOSTNAME: "https://devel.herohero.co"
  HOSTNAME_SERVICES: "https://svc-devel.herohero.co"
  IO_RETRIES: 0
  GJIRAFA_PROJECT: "agmipoda"
  GJIRAFA_API_KEY: "$GJIRAFA_API_KEY_DEVEL"
  GJIRAFA_IMAGE_KEY: "$GJIRAFA_IMAGE_KEY_DEVEL"
  STRIPE_API_KEY_EU: "$STRIPE_API_KEY_EU_TEST"
  STRIPE_API_KEY_US: "$STRIPE_API_KEY_US_TEST"

.variables-devel-list: &variables-devel-list
  <<: *variables-list
  PRODUCTION: "false"
  ENVIRONMENT: "devel"
  HOSTNAME: "https://devel.herohero.co"
  HOSTNAME_SERVICES: "https://svc-devel.herohero.co"
  IO_RETRIES: 0
  MIN_INSTANCES: 1
  MAX_INSTANCES: 2
  SQL_DATABASE_NAME: devel-herohero-db
  ORIGIN_POLICY: "https://local.herohero.co:3000;https://devel.herohero.co;https://preview.herohero.co"
  GJIRAFA_PROJECT: "agmipoda"
  GJIRAFA_API_KEY: "$GJIRAFA_API_KEY_DEVEL"
  GJIRAFA_IMAGE_KEY: "$GJIRAFA_IMAGE_KEY_DEVEL"
  STRIPE_API_KEY_EU: "$STRIPE_API_KEY_EU_DEVEL"
  STRIPE_API_KEY_US: "$STRIPE_API_KEY_US_DEVEL"

.variables-staging-list: &variables-staging-list
  <<: *variables-list
  PRODUCTION: "true"
  ENVIRONMENT: "staging"
  HOSTNAME: "https://staging.herohero.co"
  HOSTNAME_SERVICES: "https://svc-staging.herohero.co"
  MIN_INSTANCES: 1
  MAX_INSTANCES: 2
  SQL_DATABASE_NAME: prod-herohero-db
  ORIGIN_POLICY: "https://local.herohero.co:3000;https://staging.herohero.co"
  GJIRAFA_PROJECT: "agmipobm"
  GJIRAFA_API_KEY: "$GJIRAFA_API_KEY_PROD"
  GJIRAFA_IMAGE_KEY: "$GJIRAFA_IMAGE_KEY_PROD"
  STRIPE_API_KEY_EU: "$STRIPE_API_KEY_EU_PROD"
  STRIPE_API_KEY_US: "$STRIPE_API_KEY_US_PROD"

.variables-prod-list: &variables-prod-list
  <<: *variables-list
  PRODUCTION: "true"
  ENVIRONMENT: "prod"
  HOSTNAME: "https://herohero.co"
  HOSTNAME_SERVICES: "https://svc-prod.herohero.co"
  MIN_INSTANCES: 1
  # Default instances limit is almost unbounded, which could be observed when sending
  # thousands of emails to our users about changing terms. I argue it is better be
  # conservative and limit the instances a priori and only raise the limit if absolutely
  # needed. This is especially important for HTTP functions which are not scheduled and
  # will fail immediately if there is not enough instances for processing.
  MAX_INSTANCES: 10
  SQL_DATABASE_NAME: prod-herohero-db
  ORIGIN_POLICY: "https://herohero.co"
  GJIRAFA_PROJECT: "agmipobm"
  GJIRAFA_API_KEY: "$GJIRAFA_API_KEY_PROD"
  GJIRAFA_IMAGE_KEY: "$GJIRAFA_IMAGE_KEY_PROD"
  STRIPE_API_KEY_EU: "$STRIPE_API_KEY_EU_PROD"
  STRIPE_API_KEY_US: "$STRIPE_API_KEY_US_PROD"

.variables-build:
  variables:
    <<: *variables-build-list

.variables-devel:
  variables:
    <<: *variables-devel-list

.variables-staging:
  variables:
    <<: *variables-staging-list

.variables-prod:
  extends:
    - .run-only-manually
  variables:
    <<: *variables-prod-list

# A trick to "natively" define jobs in subdirectory .gitlab-ci.yml files: if a job is named
# CloudRun/Http4k/build, we change directory into CloudRun/Http4k first (unless job overrides before_script).
before_script:
  - cd ${CI_JOB_NAME%/*}

.job-with-retries:
  tags:
    - heroheroco
  dependencies: [] # default to empty dependencies, let jobs override
  # this allows automatic cancellation of multiple-running job pipelines
  interruptible: true
  # we use preemptible VM to run the jobs, retry in case of system error (e.g. VM restart)
  retry:
    max: 2
    when: runner_system_failure

.run-always:
  extends: 
    - .job-with-retries
  rules:
    # Ignore unconditionally from schedules.
    - if: '$CI_PIPELINE_SOURCE == "schedule"'
      when: never
    # skip in merge request pipelines (not in normal "branch" ones), prevent creation of "detached" pipelines
    - if: '$CI_MERGE_REQUEST_EVENT_TYPE == "detached"'
      when: never
    - when: always

.run-always-on-main-or-manually:
  extends:
    - .job-with-retries
  # we must allow to merge without running this job
  allow_failure: true
  rules:
    # Ignore unconditionally from schedules.
    - if: '$CI_PIPELINE_SOURCE == "schedule"'
      when: never
    # skip in merge request pipelines (not in normal "branch" ones), prevent creation of "detached" pipelines
    - if: '$CI_MERGE_REQUEST_EVENT_TYPE == "detached"'
      when: never
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: always
    - when: manual

.run-only-manually:
  extends:
    - .job-with-retries
  # we must allow to merge without running this job
  allow_failure: true
  rules:
    # Ignore unconditionally from schedules.
    - if: '$CI_PIPELINE_SOURCE == "schedule"'
      when: never
    # skip in merge request pipelines (not in normal "branch" ones), prevent creation of "detached" pipelines
    - if: '$CI_MERGE_REQUEST_EVENT_TYPE == "detached"'
      when: never
    - when: manual

.Function/variables-build:
  extends:
    - .variables-build

.Function/variables-devel:
  stage: deploy-devel
  extends:
    - .variables-devel
    - .run-always-on-main-or-manually
  variables:
    <<: *variables-devel-list
    ENVIRONMENT_FUNCTION_NAME: "devel-$FUNCTION_NAME"
    TRIGGER: "--trigger-topic=devel-$TOPIC"
    ENVIRONMENT_ENV_VARS: "CLOUD_PROJECT=$CLOUD_PROJECT,CLOUD_ZONE=$CLOUD_ZONE,CLOUD_REGION=$CLOUD_REGION,PRODUCTION=false,ENVIRONMENT=devel,HOSTNAME=https://devel.herohero.co,HOSTNAME_SERVICES=https://svc-devel.herohero.co"
    MEMORY: 512Mi
    SERVICE_ACCOUNT: "<EMAIL>"
    TIMEOUT: "120s"
    SENTRY_DSN: https://<EMAIL>/****************

.Function/variables-staging:
  stage: deploy-staging
  extends:
    - .variables-staging
    - .run-always-on-main-or-manually
  variables:
    <<: *variables-staging-list
    ENVIRONMENT_FUNCTION_NAME: "staging-$FUNCTION_NAME"
    TRIGGER: "--trigger-topic=staging-$TOPIC"
    ENVIRONMENT_ENV_VARS: "CLOUD_PROJECT=$CLOUD_PROJECT,CLOUD_ZONE=$CLOUD_ZONE,CLOUD_REGION=$CLOUD_REGION,PRODUCTION=true,ENVIRONMENT=staging,HOSTNAME=https://staging.herohero.co,HOSTNAME_SERVICES=https://svc-staging.herohero.co"
    MEMORY: 512Mi
    SERVICE_ACCOUNT: "<EMAIL>"
    TIMEOUT: "120s"
    SENTRY_DSN: https://<EMAIL>/****************

.Function/variables-prod:
  stage: deploy-prod
  extends:
    - .variables-prod
    - .run-only-manually
  variables:
    <<: *variables-prod-list
    ENVIRONMENT_FUNCTION_NAME: "prod-$FUNCTION_NAME"
    TRIGGER: "--trigger-topic=prod-$TOPIC"
    ENVIRONMENT_ENV_VARS: "CLOUD_PROJECT=$CLOUD_PROJECT,CLOUD_ZONE=$CLOUD_ZONE,CLOUD_REGION=$CLOUD_REGION,PRODUCTION=true,ENVIRONMENT=prod,HOSTNAME=https://herohero.co,HOSTNAME_SERVICES=https://svc-prod.herohero.co"
    MEMORY: 512Mi
    SERVICE_ACCOUNT: "<EMAIL>"
    TIMEOUT: "120s"
    SENTRY_DSN: https://<EMAIL>/****************
